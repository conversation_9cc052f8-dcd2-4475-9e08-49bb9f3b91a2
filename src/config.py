import os
from datetime import datetime

class Config:
    # Exchange settings
    EXCHANGE_API_KEY = os.getenv("EXCHANGE_API_KEY", "")
    EXCHANGE_SECRET_KEY = os.getenv("EXCHANGE_SECRET_KEY", "")
    
    # Telegram settings
    TELEGRAM_TOKEN = os.getenv("TELEGRAM_TOKEN", "")
    TELEGRAM_ALLOWED_USERS = [int(id) for id in os.getenv("TELEGRAM_ALLOWED_USERS", "").split(",") if id]
    
    # Database settings
    DB_HOST = os.getenv("DB_HOST", "localhost")
    DB_PORT = os.getenv("DB_PORT", "5432")
    DB_NAME = os.getenv("DB_NAME", "trading")
    DB_USER = os.getenv("DB_USER", "trading")
    DB_PASSWORD = os.getenv("DB_PASSWORD", "123456")
    
    # General settings
    TRADING_MODE = os.getenv("TRADING_MODE", "backtest")  # 'live' or 'backtest'
    
    # Backtest settings
    BACKTEST_START_TIME = os.getenv("BACKTEST_START_TIME", "2023-01-01T00:00:00")
    BACKTEST_END_TIME = os.getenv("BACKTEST_END_TIME", "2023-01-31T23:59:59")
    BACKTEST_INITIAL_BALANCE = {
        "ETH": float(os.getenv("BACKTEST_INITIAL_ETH", "0")),
        "BTC": float(os.getenv("BACKTEST_INITIAL_BTC", "0"))
    }
    BACKTEST_TRADING_FEE = float(os.getenv("BACKTEST_TRADING_FEE", "0.001"))  # 0.1%
    BACKTEST_TIME_STEP = int(
        os.getenv("BACKTEST_TIME_STEP", "60"))  # 1 minute in seconds
    
    # Strategy settings
    # Default to mean_reversion
    STRATEGY_NAME = os.getenv("STRATEGY_NAME", "mean_reversion")

    @classmethod
    def get_backtest_start_time(cls) -> datetime:
        return datetime.fromisoformat(cls.BACKTEST_START_TIME)
    
    @classmethod
    def get_backtest_end_time(cls) -> datetime:
        return datetime.fromisoformat(cls.BACKTEST_END_TIME) 
