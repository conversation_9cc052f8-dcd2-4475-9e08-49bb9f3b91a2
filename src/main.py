import asyncio
import signal
from config import Config
from db.trading import TradingDB
from exchange.binance import BinanceExchange
from exchange.mock_exchange import MockExchange
from strategies.base_strategy import BaseStrategy
from strategies.strategy_factory import StrategyFactory
from utils.logging import Logger
from utils.telegram_client import TelegramClient
from decimal import getcontext
import argparse
from datetime import datetime
import traceback

# Global variables
strategy = None
shutdown_event = asyncio.Event()

async def get_bot_status() -> str:
    """Get current bot status"""
    global strategy
    status = []
    if strategy:
        status.append("\nStrategy Status:")
        strategy_status = await strategy.get_status()
        for line in strategy_status:
            status.append(line)
    return "\n".join(status)

async def stop_bot():
    """Stop the trading bot"""
    global strategy
    await strategy.baseStop()
    
async def start_bot():
    """Start the trading bot"""
    global strategy
    await strategy.baseStart()


async def run_strategy(strategy: BaseStrategy, is_backtest: bool = False):
    """Run trading strategy in separate task"""
    logger = Logger.get_logger()
    try:
        await strategy.baseStart()
        
        if is_backtest:
            # For backtest, run as fast as possible
            logger.info("Starting backtest simulation...")
            mock_exchange = strategy.exchange
            usdt_start = await mock_exchange.get_balance_total()
            logger.info(f"Start with USDT: {usdt_start}")
            
            while not shutdown_event.is_set():
                # Run strategy loop
                await strategy.loop_with_lock()
                
                # Advance time for backtest
                continue_backtest = await mock_exchange.advance_time()
                if not continue_backtest:
                    logger.info("Backtest completed - reached end time")

                    # Print final balances for backtest

                    logger.info("Backtest Results:")
                    usdt_end = await mock_exchange.get_balance_total()
                    logger.info(f"End with USDT: {usdt_end}")

                    profit_usdt_percentage = (
                        usdt_end - usdt_start) / usdt_start * 100
                    logger.info(
                        f"Profit USDT:{profit_usdt_percentage}%")
                    shutdown_event.set()
                    break
                await asyncio.sleep(0.001)
        else:
            # For live trading, run at regular intervals
            while not shutdown_event.is_set():                
                await strategy.loop_with_lock()
                await asyncio.sleep(1)
                
    except asyncio.CancelledError:
        pass
    except Exception as e:
        logger.error(f"Error in strategy loop: traceback: {traceback.format_exc()}")
    finally:
        await strategy.stop()

def handle_signal():
    logger = Logger.get_logger()
    logger.info("Signal received, stopping...")
    shutdown_event.set()

async def main():
    """Main application entry point"""
    getcontext().prec = 32
    global strategy, shutdown_event
    
    # Parse command line arguments
    parser = argparse.ArgumentParser(description='Trading Bot')
    parser.add_argument('--mode', choices=['live', 'backtest'], default=Config.TRADING_MODE,
                        help='Trading mode: live or backtest')
    parser.add_argument('--strategy', type=str, default=Config.STRATEGY_NAME,
                        help='Strategy to use')
    parser.add_argument('--start', type=str, default=Config.BACKTEST_START_TIME,
                        help='Backtest start time (ISO format)')
    parser.add_argument('--end', type=str, default=Config.BACKTEST_END_TIME,
                        help='Backtest end time (ISO format)')
    parser.add_argument('--step', type=int, default=Config.BACKTEST_TIME_STEP,
                        help='Backtest time step in seconds')
    
    args = parser.parse_args()
    
    # Update config based on arguments
    Config.TRADING_MODE = args.mode
    Config.STRATEGY_NAME = args.strategy
    Config.BACKTEST_START_TIME = args.start
    Config.BACKTEST_END_TIME = args.end
    Config.BACKTEST_TIME_STEP = args.step
    
    # Setup logger
    logger = Logger.setup(
        name="trading",
        log_to_file=True,
        log_dir="logs"
    )
    logger.info(
        f"Starting trading bot in {Config.TRADING_MODE} mode with strategy {Config.STRATEGY_NAME}...")
    
    if Config.TRADING_MODE == 'backtest':
        logger.info(f"Backtest period: {Config.BACKTEST_START_TIME} to {Config.BACKTEST_END_TIME}")
    
    try:
        # Create tasks for Telegram bot and strategy
        telegram = TelegramClient(
            token=Config.TELEGRAM_TOKEN,
            allowed_user_ids=Config.TELEGRAM_ALLOWED_USERS
        )
        telegram.set_status_callback(get_bot_status)
        telegram.set_stop_callback(stop_bot)
        telegram.set_start_callback(start_bot)
        
        # Only start Telegram in live mode
        if Config.TRADING_MODE == 'live':
            await telegram.start()
        
        db = TradingDB(
            host=Config.DB_HOST,
            port=Config.DB_PORT,
            dbname=Config.DB_NAME,
            user=Config.DB_USER,
            password=Config.DB_PASSWORD
        )
        logger.info("Database connection initialized")
        
        # Create exchange based on mode
        if Config.TRADING_MODE == 'backtest':
            exchange = MockExchange(
                api_key=Config.EXCHANGE_API_KEY,
                secret_key=Config.EXCHANGE_SECRET_KEY
            )
            logger.info("Using MockExchange for backtesting")
        else:
            exchange = BinanceExchange(
                api_key=Config.EXCHANGE_API_KEY,
                secret_key=Config.EXCHANGE_SECRET_KEY
            )
            logger.info("Using BinanceExchange for live trading")
        
        # Create strategy using factory
        strategy = StrategyFactory.create_strategy(
            Config.STRATEGY_NAME,
            db,
            telegram,
            exchange
        )
            
        strategy_task = asyncio.create_task(
            run_strategy(strategy, is_backtest=(Config.TRADING_MODE == 'backtest'))
        )
        
        # Wait for shutdown event
        await shutdown_event.wait()
        
        # cancel tasks
        strategy_task.cancel()
        # Wait for tasks to complete 
        try:
            await strategy_task
        except asyncio.CancelledError:
            pass
        
        if Config.TRADING_MODE == 'live':
            await telegram.stop()

    except Exception as e:
        logger.error(f"Error in main: {e}", exc_info=True)
    finally:
        logger.info("Shutdown complete")

if __name__ == "__main__":
    loop = asyncio.get_event_loop()

    for sig in (signal.SIGINT, signal.SIGTERM):
        loop.add_signal_handler(sig, handle_signal)

    try:
        loop.run_until_complete(main())
    finally:
        loop.close()
