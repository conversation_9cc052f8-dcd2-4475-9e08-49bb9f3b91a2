import logging
import sys
from datetime import datetime
from pathlib import Path
from typing import Optional

class Logger:
    _instance: Optional[logging.Logger] = None

    @classmethod
    def setup(cls, 
              name: str = "trading", 
              log_level: int = logging.INFO,
              log_to_file: bool = True,
              log_dir: str = "logs") -> logging.Logger:
        """
        Setup logger instance
        
        Args:
            name: Logger name
            log_level: Logging level
            log_to_file: Whether to log to file
            log_dir: Directory to store log files
        """
        if cls._instance is not None:
            return cls._instance

        # Create logger
        logger = logging.getLogger(name)
        logger.setLevel(log_level)
        
        # Create formatters
        detailed_formatter = logging.Formatter(
            '%(asctime)s - %(name)s - %(levelname)s - %(filename)s:%(lineno)d - %(message)s'
        )
        simple_formatter = logging.Formatter(
            '%(asctime)s - %(levelname)s - %(message)s'
        )

        # Console handler
        console_handler = logging.StreamHandler(sys.stdout)
        console_handler.setFormatter(simple_formatter)
        logger.addHandler(console_handler)

        # File handler
        if log_to_file:
            log_path = Path(log_dir)
            log_path.mkdir(parents=True, exist_ok=True)
            
            # Create new log file for each run
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            file_handler = logging.FileHandler(
                log_path / f"{name}_{timestamp}.log"
            )
            file_handler.setFormatter(detailed_formatter)
            logger.addHandler(file_handler)

        cls._instance = logger
        return logger

    @classmethod
    def get_logger(cls) -> logging.Logger:
        """Get logger instance"""
        if cls._instance is None:
            return cls.setup()
        return cls._instance 