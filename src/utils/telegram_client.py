import asyncio
from typing import Optional, Callable
from telegram import Update
from telegram.ext import Application, CommandHandler, ContextTypes, ApplicationBuilder, MessageHandler, filters
from utils.logging import Logger

class TelegramClient:
    _instance = None

    def __new__(cls, *args, **kwargs):
        if cls._instance is None:
            cls._instance = super(TelegramClient, cls).__new__(cls)
        return cls._instance

    def __init__(self, token: str, allowed_user_ids: list[int]):
        self.logger = Logger.get_logger()
        self.token = token
        self.allowed_user_ids = allowed_user_ids
        self.app: Optional[Application] = None
        self._status_callback: Optional[Callable[[], str]] = None
        self._stop_callback: Optional[Callable[[], None]] = None
        self._start_callback: Optional[Callable[[], None]] = None
            

    def _check_auth(self, user_id: int) -> bool:
        """Check if user is authorized"""
        return user_id in self.allowed_user_ids

    async def _start_command(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Handle /start command"""
        if not update.effective_user or not self._check_auth(update.effective_user.id):
            await update.message.reply_text("Unauthorized access denied.")
            return
        
        if self._start_callback:
            try:
                await self._start_callback()
                await update.message.reply_text("Bot started successfully!")
            except Exception as e:
                await update.message.reply_text(f"Failed to start bot: {e}")
        else:
            await update.message.reply_text("Start callback not set")

    async def _stop_command(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Handle /stop command"""
        if not update.effective_user or not self._check_auth(update.effective_user.id):
            await update.message.reply_text("Unauthorized access denied.")
            return
        
        if self._stop_callback:
            try:
                await self._stop_callback()
                await update.message.reply_text("Bot stopped successfully!")
            except Exception as e:
                await update.message.reply_text(f"Failed to stop bot: {e}")
        else:
            await update.message.reply_text("Stop callback not set")

    async def _status_command(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Handle /status command"""
        if not update.effective_user or not self._check_auth(update.effective_user.id):
            await update.message.reply_text("Unauthorized access denied.")
            return
        if self._status_callback:
            try:
                status = await self._status_callback()
                await update.message.reply_text(status)
            except Exception as e:
                await update.message.reply_text(f"Failed to get status: {e}")
        else:
            await update.message.reply_text("Status callback not set")


    async def start(self):
        """Start the Telegram bot"""
        self.logger.info("Starting Telegram bot...")
        
        self.app = ApplicationBuilder().token(self.token).build()
        # Register command handlers
        self.app.add_handler(CommandHandler("start", self._start_command))
        self.app.add_handler(CommandHandler("stop", self._stop_command))
        self.app.add_handler(CommandHandler("status", self._status_command))
        await self.app.initialize()
        await self.app.start()
        await self.app.updater.start_polling()
        self.logger.info("Telegram bot started")
        
    async def stop(self):
        """Stop the Telegram bot"""
        self.logger.info("Stopping Telegram bot...")
        
        if self.app:
            await self.app.updater.stop()
            await self.app.stop()
            await self.app.shutdown()
            self.logger.info("Telegram bot stopped")

    def set_status_callback(self, callback: Callable[[], str]):
        """Set callback for status command"""
        self._status_callback = callback

    def set_stop_callback(self, callback: Callable[[], None]):
        """Set callback for stop command"""
        self._stop_callback = callback

    def set_start_callback(self, callback: Callable[[], None]):
        """Set callback for start command"""
        self._start_callback = callback

    async def send_message(self, message: str):
        """Send message to all allowed users"""
        if not self.app:
            return
        
        for user_id in self.allowed_user_ids:
            try:
                await self.app.bot.send_message(
                    chat_id=user_id,
                    text=message
                )
            except Exception as e:
                self.logger.error(f"Failed to send message to user {user_id}: {e}") 