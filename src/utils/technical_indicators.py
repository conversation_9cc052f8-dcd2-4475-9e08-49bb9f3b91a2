from decimal import Decimal, ROUND_DOWN
import pandas as pd
from typing import Union, List

class TechnicalIndicators:
    @staticmethod
    def calculate_ema(prices: Union[List[Decimal], pd.Series], period: int, smoothing: float = 2.0) -> List[Decimal]:
        """
        Calculate Exponential Moving Average (EMA)
        
        Args:
            prices: List of price values
            period: EMA period
            smoothing: Smoothing factor (default is 2.0 which is widely used)
            
        Returns:
            List of EMA values
        """
        if isinstance(prices, pd.Series):
            prices = [Decimal(str(x)) for x in prices.tolist()]
        elif not isinstance(prices[0], Decimal):
            prices = [Decimal(str(x)) for x in prices]
            
        ema_values = []
        multiplier = Decimal(str(smoothing / (1 + period)))
        
        # First EMA value is SMA
        sma = sum(prices[:period]) / Decimal(period)
        ema_values.append(sma)
        
        # Calculate EMA for remaining periods
        for i in range(period, len(prices)):
            ema = (prices[i] * multiplier + 
                   ema_values[-1] * (Decimal('1') - multiplier))
            ema = ema.quantize(Decimal('0'), rounding=ROUND_DOWN)
            ema_values.append(ema)
            
        return ema_values

    @staticmethod
    def get_current_ema(prices: Union[List[Decimal], pd.Series], period: int) -> Decimal:
        """
        Get the most recent EMA value
        
        Args:
            prices: List of price values
            period: EMA period
            
        Returns:
            Most recent EMA value
        """
        ema_values = TechnicalIndicators.calculate_ema(prices, period)
        return ema_values[-1] if ema_values else Decimal('0')