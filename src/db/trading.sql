DROP TABLE IF EXISTS orders;
CREATE TYPE order_status AS ENUM ('BUYING', 'BOUGHT', 'SELLING', 'SOLD', 'MANUAL');

CREATE TABLE orders(
    id                   INT GENERATED BY DEFAULT AS IDENTITY,
    buy_order_no         VARCHAR(64)     NOT NULL, /*buy_order_no*/
    sell_order_no        VARCHAR(64)     DEFAULT NULL, /*sell_order_no*/

    pair                 VARCHAR(10)     NOT NULL, /*pair*/
    status               order_status DEFAULT 'BUYING',

    /*price buy*/
    buy_price            numeric(32, 16) NOT NULL DEFAULT '0', 
    buy_amount           numeric(32, 16) NOT NULL DEFAULT '0',

    buy_amount_filled    numeric(32, 16) NOT NULL DEFAULT '0',
    buy_start_at         timestamptz DEFAULT NULL,
    buy_end_at           timestamptz DEFAULT NULL,

    /*price sell*/
    sell_price           numeric(32, 16) NOT NULL DEFAULT '0', 
    sell_amount_filled   numeric(32, 16) NOT NULL DEFAULT '0',
    sell_start_at        timestamptz DEFAULT NULL,
    sell_end_at          timestamptz DEFAULT NULL
);

create unique index orders_buy_order_no_unique_idx on orders (buy_order_no);
create unique index orders_sell_order_no_unique_idx on orders (sell_order_no);

/*current balance*/
DROP TABLE IF EXISTS mean_reversion_current_balance;
create table mean_reversion_current_balance(
    id                   INT GENERATED BY DEFAULT AS IDENTITY,
    coin                 VARCHAR(10)     NOT NULL, /*coin*/
    amount               numeric(32, 16) NOT NULL DEFAULT '0',
    in_flight            boolean         NOT NULL DEFAULT FALSE, /*if the balance is in flight, it means order(s) are in flight for conversion*/
    to_coin              VARCHAR(10)     DEFAULT NULL,
    needs_bridge         boolean         NOT NULL DEFAULT FALSE, /*if the balance needs to be converted through a bridge*/
    order_symbol         VARCHAR(10)     DEFAULT NULL,
    order_id_1           VARCHAR(64)     DEFAULT NULL, /*order_id_1 & order_id_2 are used to associate the current balance with the order*/
    order_id_2           VARCHAR(64)     DEFAULT NULL, /* order_id_2 is not null when the conversion needs to be done through a bridge*/
    updated_at           timestamptz DEFAULT CURRENT_TIMESTAMP
);

/*last coin balance*/
DROP TABLE IF EXISTS mean_reversion_last_coin_value;
create table mean_reversion_last_coin_value(
    id                   INT GENERATED BY DEFAULT AS IDENTITY,
    current_balance_id   INT             NOT NULL, /*current_coin_id*/
    last_coin            VARCHAR(10)     NOT NULL, /*last_coin_id*/
    last_amount          numeric(32, 16) NOT NULL DEFAULT '0',
    updated_at           timestamptz DEFAULT CURRENT_TIMESTAMP
);

create unique index mean_reversion_current_balance_id_last_coin_unique_idx on mean_reversion_last_coin_value (current_balance_id, last_coin);

DROP TABLE IF EXISTS react_to_market_cap_current_balance;
create table react_to_market_cap_current_balance(
    id                   INT GENERATED BY DEFAULT AS IDENTITY,
    pair                 VARCHAR(10)     NOT NULL, /*pair*/
    long_term            INT        NOT NULL, /*long_term*/
    short_term           INT       NOT NULL, /*short_term*/
    order_id             VARCHAR(64)     DEFAULT NULL, /*order_id*/
    holding_base         boolean         NOT NULL DEFAULT FALSE, /*if the balance is holding base coin*/
    in_flight            boolean         NOT NULL DEFAULT FALSE, /*if the balance is in flight, it means order(s) are in flight for conversion*/
    amount               numeric(32, 16) NOT NULL DEFAULT '0',
    last_price           numeric(32, 16),
    updated_at           timestamptz DEFAULT CURRENT_TIMESTAMP
);
create unique index react_to_market_cap_current_balance_pair_long_term_short_term_unique_idx on react_to_market_cap_current_balance (pair, long_term, short_term);


/*create data for storing klines*/
DROP TABLE IF EXISTS klines;
create table klines(
    id                   INT GENERATED BY DEFAULT AS IDENTITY,
    pair                 VARCHAR(10)     NOT NULL, /*pair*/
    interval             VARCHAR(10)     NOT NULL, /*interval*/
    open_time            timestamptz     NOT NULL, /*open_time*/
    open_price           numeric(32, 16) NOT NULL DEFAULT '0', /*open_price*/
    high_price           numeric(32, 16) NOT NULL DEFAULT '0', /*high_price*/
    low_price            numeric(32, 16) NOT NULL DEFAULT '0', /*low_price*/
    close_price          numeric(32, 16) NOT NULL DEFAULT '0', /*close_price*/
    volume               numeric(32, 16) NOT NULL DEFAULT '0', /*volume*/
    quote_volume         numeric(32, 16) NOT NULL DEFAULT '0', /*quote_volume*/
    number_of_trades     INT             NOT NULL DEFAULT '0', /*number_of_trades*/
    taker_buy_base_asset numeric(32, 16) NOT NULL DEFAULT '0', /*taker_buy_base_asset*/ 
    taker_buy_quote_asset numeric(32, 16) NOT NULL DEFAULT '0', /*taker_buy_quote_asset*/
    ignore               boolean         NOT NULL DEFAULT FALSE /*ignore*/
);

create unique index klines_pair_interval_open_time_unique_idx on klines (pair, interval, open_time);