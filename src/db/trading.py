import psycopg2
from psycopg2.extras import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>
from typing import Dict, Any, Optional, List
from decimal import Decimal
from datetime import datetime
import time

class TradingDB:
    def __init__(self, host: str, port: str, dbname: str, user: str, password: str):
        # retry if connection error
        while True:
            try:
                self.conn = psycopg2.connect(
                    host=host,
                    port=port,
                    dbname=dbname,
                    user=user,
                    password=password
                )
                break
            except psycopg2.OperationalError as e:
                print(f"Error connecting to database: {e}")
                time.sleep(1)
                continue

    # Order related methods
    async def create_order(self, buy_order_no: str, pair: str, buy_price: Decimal, buy_amount: Decimal) -> int:
        with self.conn.cursor() as cur:
            cur.execute("""
                INSERT INTO orders (buy_order_no, pair, buy_price, buy_amount)
                VALUES (%(buy_order_no)s, %(pair)s, %(buy_price)s, %(buy_amount)s)
                RETURNING id
            """, {
                'buy_order_no': buy_order_no,
                'pair': pair,
                'buy_price': buy_price,
                'buy_amount': buy_amount
            })
            self.conn.commit()
            return cur.fetchone()[0]

    async def update_order_buy(self, buy_order_no: str, buy_amount_filled: Decimal, buy_end_at: datetime) -> bool:
        with self.conn.cursor() as cur:
            cur.execute("""
                UPDATE orders
                SET buy_amount_filled = %(buy_amount_filled)s,
                    buy_end_at = %(buy_end_at)s,
                    status = 'BOUGHT'
                WHERE buy_order_no = %(buy_order_no)s
            """, {
                'buy_order_no': buy_order_no,
                'buy_amount_filled': buy_amount_filled,
                'buy_end_at': buy_end_at
            })
            self.conn.commit()
            return True

    async def update_order_sell(self, sell_order_no: str, sell_price: Decimal,
                              sell_amount_filled: Decimal, sell_end_at: datetime) -> bool:
        with self.conn.cursor() as cur:
            cur.execute("""
                UPDATE orders
                SET sell_order_no = %(sell_order_no)s,
                    sell_price = %(sell_price)s,
                    sell_amount_filled = %(sell_amount_filled)s,
                    sell_end_at = %(sell_end_at)s,
                    status = 'SOLD'
                WHERE buy_order_no = %(buy_order_no)s
            """, {
                'sell_order_no': sell_order_no,
                'sell_price': sell_price,
                'sell_amount_filled': sell_amount_filled,
                'sell_end_at': sell_end_at
            })
            self.conn.commit()
            return True

    # Current balance methods
    async def update_current_balance(self, coin: str, amount: Decimal,
                                   in_flight: bool = False,
                                   order_id_1: Optional[str] = None,
                                   order_id_2: Optional[str] = None) -> int:
        with self.conn.cursor() as cur:
            cur.execute("""
                INSERT INTO mean_reversion_current_balance
                (coin, amount, in_flight, order_id_1, order_id_2)
                VALUES (
                    %(coin)s,
                    %(amount)s,
                    %(in_flight)s,
                    %(order_id_1)s,
                    %(order_id_2)s
                )
                RETURNING id
            """, {
                'coin': coin,
                'amount': amount,
                'in_flight': in_flight,
                'order_id_1': order_id_1,
                'order_id_2': order_id_2
            })
            self.conn.commit()
            return cur.fetchone()[0]

    async def current_balance_first_order_placed(self, id: int, to_coin: str, needs_bridge: bool, order_id_1: str, order_symbol: str) -> bool:
        with self.conn.cursor() as cur:
            cur.execute("""
                UPDATE mean_reversion_current_balance
                SET in_flight = TRUE,
                    to_coin = %(to_coin)s,
                    needs_bridge = %(needs_bridge)s,
                    order_id_1 = %(order_id_1)s,
                    order_symbol = %(order_symbol)s,
                    updated_at = CURRENT_TIMESTAMP
                WHERE id = %(id)s
            """, {'id': id, 'to_coin': to_coin, 'needs_bridge': needs_bridge, 'order_id_1': order_id_1, 'order_symbol': order_symbol})
            self.conn.commit()
            return True

    async def current_balance_second_order_placed(self, id: int, order_id_2: str, order_symbol: str) -> bool:
        with self.conn.cursor() as cur:
            cur.execute("""
                UPDATE mean_reversion_current_balance
                SET order_id_2 = %(order_id_2)s,
                    order_id_1 = NULL,
                    order_symbol = %(order_symbol)s,
                    updated_at = CURRENT_TIMESTAMP
                WHERE id = %(id)s
            """, {'id': id, 'order_id_2': order_id_2, 'order_symbol': order_symbol})
            self.conn.commit()
            return True

    async def current_balance_in_flight_completed(self, id: int, amount: Decimal) -> bool:
        with self.conn.cursor() as cur:
            cur.execute("""
                UPDATE mean_reversion_current_balance
                SET in_flight = FALSE,
                    order_id_1 = NULL,
                    order_id_2 = NULL,
                    order_symbol = NULL,
                    coin = to_coin,
                    amount = %(amount)s,
                    updated_at = CURRENT_TIMESTAMP
                WHERE id = %(id)s
            """, {'id': id, 'amount': amount} )
            self.conn.commit()
            return True

    async def current_balance_in_flight_canceled(self, id: int) -> bool:
        with self.conn.cursor() as cur:
            cur.execute("""
                UPDATE mean_reversion_current_balance
                SET in_flight = FALSE,
                    updated_at = CURRENT_TIMESTAMP
                WHERE id = %(id)s
            """, {'id': id})
            self.conn.commit()
            return True

    async def get_current_balance(self, coin: str) -> Optional[Dict[str, Any]]:
        with self.conn.cursor(cursor_factory=RealDictCursor) as cur:
            cur.execute("""
                SELECT id, coin, amount, in_flight, to_coin, needs_bridge, order_symbol, order_id_1, order_id_2, updated_at
                FROM mean_reversion_current_balance
                WHERE coin = %(coin)s
            """, {'coin': coin})
            return cur.fetchone()

    async def get_all_current_balances(self) -> List[Dict[str, Any]]:
        with self.conn.cursor(cursor_factory=RealDictCursor) as cur:
            cur.execute("""
                SELECT id, coin, amount, in_flight, to_coin, needs_bridge, order_symbol, order_id_1, order_id_2, updated_at
                FROM mean_reversion_current_balance
            """)
            return cur.fetchall()

    async def get_in_flight_balances(self) -> List[Dict[str, Any]]:
        with self.conn.cursor(cursor_factory=RealDictCursor) as cur:
            cur.execute("""
                SELECT id, coin, amount, in_flight, to_coin, needs_bridge, order_symbol, order_id_1, order_id_2, updated_at
                FROM mean_reversion_current_balance
                WHERE in_flight = TRUE
            """)
            return cur.fetchall()

    async def deduct_bnb_fee_from_balances(self, id: int, fee: Decimal) -> bool:
        with self.conn.cursor() as cur:
            cur.execute("""
                UPDATE mean_reversion_current_balance
                SET amount = amount - %(fee)s,
                    updated_at = CURRENT_TIMESTAMP
                WHERE id = %(id)s
            """, {'id': id, 'fee': fee})
            self.conn.commit()
            return True

    # Last coin balance methods
    async def update_last_coin_value(self, current_balance_id: int, last_coin: str, last_amount: Decimal) -> bool:
        with self.conn.cursor() as cur:
            cur.execute("""
                INSERT INTO mean_reversion_last_coin_value
                (current_balance_id, last_coin, last_amount)
                VALUES (%(current_balance_id)s, %(last_coin)s, %(last_amount)s)
                ON CONFLICT (current_balance_id, last_coin) DO UPDATE
                SET last_amount = %(last_amount)s,
                    updated_at = CURRENT_TIMESTAMP
            """, {
                'current_balance_id': current_balance_id,
                'last_coin': last_coin,
                'last_amount': last_amount
            })
            self.conn.commit()
            return True

    async def get_last_coin_value(self, current_balance_id: int, last_coin: str) -> Optional[Dict[str, Any]]:
        with self.conn.cursor(cursor_factory=RealDictCursor) as cur:
            cur.execute("""
                SELECT id, current_balance_id, last_coin, last_amount, updated_at
                FROM mean_reversion_last_coin_value
                WHERE current_balance_id = %(current_balance_id)s AND last_coin = %(last_coin)s
            """, {
                'current_balance_id': current_balance_id,
                'last_coin': last_coin
            })
            return cur.fetchone()

    async def update_react_to_market_cap_current_balance(self, pair: str, long_term: int, short_term: int, amount: Decimal) -> int:
        with self.conn.cursor() as cur:
            cur.execute("""
                INSERT INTO react_to_market_cap_current_balance
                (pair, long_term, short_term, amount)
                VALUES (%(pair)s, %(long_term)s, %(short_term)s, %(amount)s)
                RETURNING id
            """, {
                'pair': pair,
                'long_term': long_term,
                'short_term': short_term,
                'amount': amount
            })
            self.conn.commit()
            return cur.fetchone()[0]

    async def get_react_to_market_cap_all_current_balances(self) -> List[Dict[str, Any]]:
        with self.conn.cursor(cursor_factory=RealDictCursor) as cur:
            cur.execute("""
                SELECT id, pair, long_term, short_term, order_id, holding_base, in_flight, amount, last_price, updated_at
                FROM react_to_market_cap_current_balance
            """)
            return cur.fetchall()

    async def react_to_market_cap_current_balance_order_placed(self, id: int, order_id: str) -> bool:
        with self.conn.cursor() as cur:
            cur.execute("""
                UPDATE react_to_market_cap_current_balance
                SET order_id = %(order_id)s,
                    in_flight = TRUE,
                    updated_at = CURRENT_TIMESTAMP
                WHERE id = %(id)s
            """, {'id': id, 'order_id': order_id})
            self.conn.commit()
            return True

    async def react_to_market_cap_current_balance_order_completed(self, id: int, amount: Decimal, holding_base: bool, last_price: Decimal) -> bool:
        with self.conn.cursor() as cur:
            cur.execute("""
                UPDATE react_to_market_cap_current_balance
                SET order_id = NULL,
                    in_flight = FALSE,
                    holding_base = %(holding_base)s,
                    amount = %(amount)s,
                    last_price = %(last_price)s,
                    updated_at = CURRENT_TIMESTAMP
                WHERE id = %(id)s
            """, {'id': id, 'amount': amount, 'holding_base': holding_base, 'last_price': last_price})
            self.conn.commit()
            return True

    async def react_to_market_cap_deduct_bnb_fee_from_balances(self, id: int, fee: Decimal) -> bool:
        with self.conn.cursor() as cur:
            cur.execute("""
                UPDATE react_to_market_cap_current_balance
                SET amount = amount - %(fee)s,
                    updated_at = CURRENT_TIMESTAMP
                WHERE id = %(id)s
            """, {'id': id, 'fee': fee})
            self.conn.commit()
            return True

    # write methods for manipulating klines
    async def insert_klines(self, klines: List[Dict[str, Any]]) -> bool:
        """
        Insert klines data with conflict resolution.
        If a kline with the same (pair, interval, open_time) already exists, update it.
        """
        if not klines:
            return True

        with self.conn.cursor() as cur:
            cur.executemany("""
                INSERT INTO klines
                (pair, interval, open_time, open_price, high_price, low_price, close_price, volume, quote_volume, number_of_trades, taker_buy_base_asset, taker_buy_quote_asset, ignore)
                VALUES (%(pair)s, %(interval)s, %(open_time)s, %(open_price)s, %(high_price)s, %(low_price)s, %(close_price)s, %(volume)s, %(quote_volume)s, %(number_of_trades)s, %(taker_buy_base_asset)s, %(taker_buy_quote_asset)s, %(ignore)s)
                ON CONFLICT (pair, interval, open_time)
                DO UPDATE SET
                    open_price = EXCLUDED.open_price,
                    high_price = EXCLUDED.high_price,
                    low_price = EXCLUDED.low_price,
                    close_price = EXCLUDED.close_price,
                    volume = EXCLUDED.volume,
                    quote_volume = EXCLUDED.quote_volume,
                    number_of_trades = EXCLUDED.number_of_trades,
                    taker_buy_base_asset = EXCLUDED.taker_buy_base_asset,
                    taker_buy_quote_asset = EXCLUDED.taker_buy_quote_asset,
                    ignore = EXCLUDED.ignore
            """, klines)
            self.conn.commit()
            return True

    async def get_klines(self, pair: str, interval: str, start_time: datetime, end_time: datetime) -> List[Dict[str, Any]]:
        with self.conn.cursor(cursor_factory=RealDictCursor) as cur:
            cur.execute("""
                SELECT id, pair, interval, open_time, open_price, high_price, low_price, close_price, volume, quote_volume, number_of_trades, taker_buy_base_asset, taker_buy_quote_asset, ignore
                FROM klines
                WHERE pair = %(pair)s AND interval = %(interval)s AND open_time >= %(start_time)s AND open_time <= %(end_time)s
            """, {
                'pair': pair,
                'interval': interval,
                'start_time': start_time,
                'end_time': end_time
            })
            return cur.fetchall()
