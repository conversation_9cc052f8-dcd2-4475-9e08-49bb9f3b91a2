import asyncio
from typing import Dict, Any, Callable, Optional
from binance import Async<PERSON>lient, BinanceSocketManager
from .execution_report import ExecutionReport
from utils.logging import Logger

class WebSocketClient:
    def __init__(self, api_key: str, secret_key: str):
        self.logger = Logger.get_logger()
        self._api_key = api_key
        self._secret_key = secret_key
        self.client: Optional[AsyncClient] = None
        self.bm: Optional[BinanceSocketManager] = None
        self._order_callback: Optional[Callable[[ExecutionReport], None]] = None
        self._running = False
        self._task: Optional[asyncio.Task] = None

    async def start(self):
        """Start the WebSocket client"""
        if self._running:
            return
        
        self.logger.info("Starting WebSocket client...")
        # Initialize AsyncClient
        self.client = await AsyncClient.create(self._api_key, self._secret_key)
        self.bm = BinanceSocketManager(self.client)
        
        self._running = True
        self._task = asyncio.create_task(self._run())
        self.logger.info("WebSocket client started")

    async def stop(self):
        """Stop the WebSocket client"""
        self.logger.info("Stopping WebSocket client...")
        self._running = False
        if self._task:
            self._task.cancel()
            try:
                await self._task
            except asyncio.CancelledError:
                pass
        if self.client:
            await self.client.close_connection()
        self.logger.info("WebSocket client stopped")

    async def _run(self):
        """Main WebSocket loop"""
        try:
            # Start user socket to get order updates
            async with self.bm.user_socket() as user_stream:
                self.logger.info("WebSocket user stream connected")
                while self._running:
                    try:
                        msg = await user_stream.recv()
                        self.logger.info(f"Received message: {msg}")
                        if msg['e'] == 'executionReport':  # Order update
                            execution_report = ExecutionReport.from_binance_response(msg)
                            await self._handle_order_update(execution_report)
                    except Exception as e:
                        self.logger.error(f"Error in WebSocket loop: {e}", exc_info=True)
                        await asyncio.sleep(5)
        finally:
            if self.client:
                await self.client.close_connection()

    async def _handle_order_update(self, execution_report: ExecutionReport):
        """Handle order update events"""
        if self._order_callback:
            await self._order_callback(execution_report)

    def set_order_callback(self, callback: Callable[[ExecutionReport], None]):
        """Set callback for all order updates"""
        self._order_callback = callback
