from abc import ABC, abstractmethod
from typing import Dict, Any, List, Optional, Callable, Tuple
from decimal import Decimal
from .execution_report import ExecutionReport
from .trade import Trade
from .balance import Balance
from .ticker import Ticker
from utils.logging import Logger
class BaseExchange(ABC):
    @abstractmethod
    def __init__(self):
        self.logger = Logger.get_logger()
        self.order_update_callback: Optional[Callable[[ExecutionReport], None]] = None

    @abstractmethod
    def start(self):
        pass

    @abstractmethod
    def stop(self):
        pass

    @abstractmethod
    async def get_ticker(self, symbol: str) -> Ticker:
        """
        Get current price information for a trading pair

        Args:
            symbol: Trading pair symbol (e.g. 'BTC/USDT')

        Returns:
            Ticker object containing price and volume information
        """
        pass

    @abstractmethod
    async def get_exchange_info(self) -> Dict[str, Any]:
        """
        Retrieve exchange information including filters like PRICE_FILTER and LOT_SIZE.

        Returns:
            Dictionary containing exchange information for all trading pairs
        """
        pass

    @abstractmethod
    async def get_lot_size(self, symbol: str) -> <PERSON><PERSON>[Decimal, Decimal, Decimal]:
        """Get lot size for a symbol"""
        pass

    @abstractmethod
    async def get_price_filter(self, symbol: str) -> Tuple[Decimal, Decimal, Decimal]:
        """Get price filter for a symbol"""
        pass

    @abstractmethod
    async def create_order(
        self,
        symbol: str,
        type: str,
        side: str,
        amount: Decimal = None,
        quote_amount: Decimal = None,
        price: Decimal = None,
        client_order_id: str = None
    ) -> str:
        """
        Create a new order on the exchange

        Args:
            symbol: Trading pair symbol
            type: Order type ('limit' or 'market')
            side: Order side ('buy' or 'sell')
            amount: Amount to trade
            price: Price for limit orders (optional)
            client_order_id: Custom order ID (optional)

        Returns:
            Order object containing order information
        """
        pass

    @abstractmethod
    async def get_trade(self, symbol: str, id: int) -> Trade:
        """
        Get trade information by order ID

        Args:
            symbol: Trading pair symbol
            id: Order ID

        Returns:
            Trade object containing trade information
        """
        pass

    @abstractmethod
    async def get_order_status(self, symbol: str, client_order_id: str) -> ExecutionReport:
        """
        Get order status by client order ID

        Args:
            symbol: Trading pair symbol
            client_order_id: Custom client order ID

        Returns:
            ExecutionReport object containing order status information
        """
        pass


    @abstractmethod
    async def get_balance(self, currency: str = None) -> Balance:
        """
        Get account balance information

        Args:
            currency: Optional currency code (e.g. 'BTC', 'USDT').
                     If not provided, returns balance for all currencies.

        Returns:
            Balance object containing balance information
        """
        pass

    @abstractmethod
    async def start(self):
        """Start the exchange connection"""
        pass

    @abstractmethod
    async def stop(self):
        """Stop the exchange connection"""
        pass

    @abstractmethod
    def set_order_update_callback(self, callback: Callable[[ExecutionReport], None]):
        """Set callback for all order updates"""
        pass

    @abstractmethod
    async def get_lot_size(self, symbol: str) -> Tuple[Decimal, Decimal, Decimal]:
        """Get lot size for a symbol"""
        pass

    @abstractmethod
    async def get_price_filter(self, symbol: str) -> Tuple[Decimal, Decimal, Decimal]:
        """Get price filter for a symbol"""
        pass

    @abstractmethod
    async def get_historical_prices(self, symbol: str, lookback_periods: int, interval: str = '30m') -> List[Decimal]:
        """Get historical closing prices for a symbol"""
        pass

    async def get_balance_total(self) -> tuple[Decimal]:
        """Get account balance"""
        total_usdt = Decimal(0)
        balance = await self.get_balance()

        for asset, asset_balance in balance.balances.items():
            amount = asset_balance.free + asset_balance.locked
            # culculate the worth of BTC and USDT
            if asset == "USDT":
                total_usdt += amount
                self.logger.info(f"USDT: {amount}")
            else:
                self.logger.info(f"{asset}: {amount}")
                # get the price of the asset
                ticker = await self.get_ticker(f"{asset}USDT")
                self.logger.info(f"{asset}USDT price: {ticker.lastPrice}")
                total_usdt += amount * ticker.lastPrice

        return total_usdt

    async def get_current_price(self, symbol: str) -> Decimal:
        """Get the current price for a symbol"""
        ticker = await self.get_ticker(symbol)
        return ticker.lastPrice

    def split_symbol(self, symbol: str) -> tuple[str, str]:
        """
        Split trading pair symbol into base and quote assets
        
        Args:
            symbol: Trading pair symbol (e.g. 'BTCUSDT', 'ETHBTC', 'DOGEBTC')
        
        Returns:
            Tuple of (base_asset, quote_asset)
        """
        # Common quote assets
        common_quote_assets = ['USDT', 'BUSD', 'BTC', 'ETH', 'BNB']

        if len(symbol) < 6:
            raise ValueError(f"Invalid symbol: {symbol}")

        # Try to match known quote assets
        for quote in common_quote_assets:
            if symbol.endswith(quote):
                return symbol[:-len(quote)], quote

        # Fallback to default 3-char split if no match
        # self.logger.warning(
        #    f"Unknown quote asset pattern in symbol {symbol}, falling back to default 3-char split")

        return symbol[:-3], symbol[-3:]
