from typing import Dict, Any, List, Optional, Callable, Tuple
from decimal import Decimal, ROUND_DOWN
from binance import AsyncClient, BinanceSocketManager
from binance.enums import *
from .base_exchange import BaseExchange
from .execution_report import ExecutionReport
from .balance import Balance
from .ticker import Ticker
from .websocket_client import WebSocket<PERSON><PERSON>
from .trade import Trade
import time

class BinanceExchange(BaseExchange):
    def __init__(self, api_key: str, secret_key: str):
        super().__init__()

        self._api_key = api_key
        self._secret_key = secret_key
        self.client: Optional[AsyncClient] = None
        self.ws_client = WebSocketClient(api_key, secret_key)
        self.ws_client.set_order_callback(self._handle_order_update)
        self.symbol_info = None
        self._klines_cache = {}  # Add cache dictionary to store historical klines

    async def _handle_order_update(self, execution_report: ExecutionReport):
        """Internal handler for order updates"""
        if self.order_update_callback:
            await self.order_update_callback(execution_report)

    async def start(self):
        """Start the exchange connection"""
        self.client = await AsyncClient.create(self._api_key, self._secret_key)
        await self.ws_client.start()

    async def stop(self):
        """Stop the exchange connection"""
        await self.ws_client.stop()
        if self.client:
            await self.client.close_connection()

    async def get_ticker(self, symbol: str) -> Ticker:
        """Get ticker information"""
        response = await self.client.get_ticker(symbol=symbol)
        return Ticker.from_binance_response(response)

    async def get_exchange_info(self) -> Dict[str, Any]:
        """Retrieve exchange information including filters like PRICE_FILTER and LOT_SIZE."""
        if not self.client:
            self.client = await AsyncClient.create(self._api_key, self._secret_key)

        exchange_info = await self.client.get_exchange_info()
        symbol_info = {}

        for symbol in exchange_info['symbols']:
            if symbol['status'] != 'TRADING':
                continue
            filters = {f['filterType']: f for f in symbol['filters']}
            if 'PRICE_FILTER' in filters and 'LOT_SIZE' in filters:
                symbol_info[symbol['symbol']] = {
                    'baseAsset': symbol['baseAsset'],
                    'quoteAsset': symbol['quoteAsset'],
                    'baseAssetPrecision': symbol['baseAssetPrecision'],
                    'quoteAssetPrecision': symbol['quoteAssetPrecision'],
                    'status': symbol['status'],
                    'PRICE_FILTER': {
                        'minPrice': Decimal(filters['PRICE_FILTER']['minPrice']),
                        'maxPrice': Decimal(filters['PRICE_FILTER']['maxPrice']),
                        'tickSize': Decimal(filters['PRICE_FILTER']['tickSize']),
                    },
                    'LOT_SIZE': {
                        'minQty': Decimal(filters['LOT_SIZE']['minQty']),
                        'maxQty': Decimal(filters['LOT_SIZE']['maxQty']),
                        'stepSize': Decimal(filters['LOT_SIZE']['stepSize']),
                    }
                }

        return symbol_info

    async def get_lot_size(self, symbol: str) -> Tuple[Decimal, Decimal, Decimal]:
        """Get lot size for a symbol"""
        if not self.symbol_info:
            self.symbol_info = await self.get_exchange_info()
        lot_size = self.symbol_info[symbol]['LOT_SIZE']
        return Decimal(lot_size['minQty']).normalize(), Decimal(lot_size['maxQty']).normalize(), Decimal(lot_size['stepSize'])

    async def get_price_filter(self, symbol: str) -> Tuple[Decimal, Decimal, Decimal]:
        """Get price filter for a symbol"""
        if not self.symbol_info:
            self.symbol_info = await self.get_exchange_info()
        price_filter = self.symbol_info[symbol]['PRICE_FILTER']
        return Decimal(price_filter['minPrice']), Decimal(price_filter['maxPrice']), Decimal(price_filter['tickSize'])

    # return basePrecision, quote Precision
    async def get_asset_precision(self, symbol: str) -> Tuple[int, int]:
        """Get price filter for a symbol"""
        if not self.symbol_info:
            self.symbol_info = await self.get_exchange_info()
        info = self.symbol_info[symbol]
        return info['baseAssetPrecision'], info['quoteAssetPrecision']

    async def adjust_base_asset_precision(self, symbol: str, amount: Decimal):
        base_precision, _ = await self.get_asset_precision(symbol)
        return self.adjust_precision(amount, base_precision)

    async def adjust_quote_asset_precision(self, symbol: str, amount: Decimal):
        _, quote_precision = await self.get_asset_precision(symbol)
        return self.adjust_precision(amount, quote_precision)

    def adjust_precision(self, amount: Decimal, precision: int):
        return amount.quantize(Decimal(10) ** -precision).normalize()

    def round_step_size(self, quantity: Decimal, step_size: Decimal) -> Decimal:
        steps = (quantity/step_size).quantize(0, rounding=ROUND_DOWN)
        return steps * step_size


    async def create_order(
        self,
        symbol: str,
        type: str,
        side: str,
        amount: Decimal = None,
        quote_amount: Decimal = None,
        price: Decimal = None,
        client_order_id: str = None,
    ) -> str:
        _, _, step_size = await self.get_lot_size(symbol)
        if amount is not None:
            amount = self.round_step_size(amount, step_size)

        """Create a new order"""
        params = {
            'symbol': symbol,
            'side': side.upper(),
            'quantity': str(amount),
        }

        if quote_amount:
            # the LOT_SIZE filter is only for amount, not quote_amount.
            # quote_amount = self.round_step_size(quote_amount, step_size)
            quote_amount = await self.adjust_quote_asset_precision(
                symbol, quote_amount)
            params['quoteOrderQty'] = str(quote_amount)
            params['quantity'] = None

        if client_order_id:
            params['newClientOrderId'] = client_order_id

        if type.upper() == 'LIMIT':
            params.update({
                'type': ORDER_TYPE_LIMIT,
                'timeInForce': TIME_IN_FORCE_GTC,
                'price': str(price)
            })
        else:
            params['type'] = ORDER_TYPE_MARKET
        self.logger.info(f"Creating order: {params}")
        response = await self.client.create_order(**params)
        self.logger.info(f"Order created: {response}")
        return response['clientOrderId']

    async def get_trade(self, symbol: str, id: str) -> Trade:
        """Get trade information"""
        trades = await self.client.get_my_trades(
            symbol=symbol,
            orderId=id
        )
        print("trades:", trades)
        if len(trades) == 0:
            return None
        qty = Decimal(0)
        quoteQty = Decimal(0)
        commissions = {}
        prices = []
        for trade in trades:
            qty += Decimal(trade['qty'])
            quoteQty += Decimal(trade['quoteQty'])
            commissionAsset = trade['commissionAsset']
            if commissionAsset not in commissions:
                commissions[commissionAsset] = Decimal(0)
            commissions[commissionAsset] += Decimal(trade['commission'])
            prices.append(Decimal(trade['price']))
        trade = Trade(
            symbol=trades[0]['symbol'],
            orderId=trades[0]['orderId'],
            qty= qty,
            quoteQty=quoteQty,
            commission=None,
            commissionAsset=None,
            commissions=commissions,
            price=prices/len(prices),
        )
        return trade

    async def get_order_status(self, symbol: str, client_order_id: str) -> ExecutionReport:
        """Get order status by client order ID"""
        try:
            order = await self.client.get_order(symbol=symbol, origClientOrderId=client_order_id)
            self.logger.info(f"Retrieved order status: {order}")

            # Create execution report from order data
            execution_report = ExecutionReport(
                id=str(order['orderId']),
                symbol=order['symbol'],
                status=order['status'].lower(),
                clientOrderId=order['clientOrderId'],
                time=order['time'],
                info=order,
                side=order['side'].lower()
            )

            return execution_report
        except Exception as e:
            self.logger.error(f"Error getting order status: {e}")
            return None

    async def get_balance(self, currency: str = None) -> Balance:
        """Get account balance"""
        response = await self.client.get_account()
        balance = Balance.from_binance_response(response)
        if currency:
            # If specific currency requested, filter the response
            filtered_balances = {
                currency: balance.balances[currency]
            } if currency in balance.balances else {}

            return Balance(
                balances=filtered_balances,
                updateTime=balance.updateTime,
                accountType=balance.accountType,
                permissions=balance.permissions,
                info=balance.info
            )
        return balance

    def set_order_update_callback(self, callback: Callable[[ExecutionReport], None]):
        """Set callback for all order updates"""
        self.order_update_callback = callback

    async def get_historical_prices(self, symbol: str, lookback_periods: int, interval: str = '30m') -> List[Decimal]:
        """
        Get historical closing prices for a symbol from Binance
        
        Args:
            symbol: Trading pair symbol
            lookback_periods: Number of periods to look back
            interval: Kline interval (e.g. '1m', '1h', '1d')
            
        Returns:
            List of historical prices
        """
        # Define cache expiration times based on interval
        expiration_seconds = {
            '1m': 15,
            # '3m': 90,
            # '5m': 150,
            # '15m': 450,
            # '30m': 900,
            # '1h': 1800,
            # '2h': 3600,
            # '4h': 14400,     # 4 hours
            # '6h': 21600,     # 6 hours
            # '8h': 28800,     # 8 hours
            # '12h': 43200,    # 12 hours
            # '1d': 86400,     # 1 day
            # '3d': 259200,    # 3 days
            # '1w': 604800,    # 1 week
        }.get(interval, 30)  # Default to 30 s if interval not recognized

        # return cached prices if available
        if not hasattr(self, '_prices_cache'):
            self._prices_cache = {}
        prices_cache_key = f"{symbol}_{interval}_{lookback_periods}_prices"
        if prices_cache_key in self._prices_cache:
            cached_prices, timestamp = self._prices_cache[prices_cache_key]
            if time.time() - timestamp < expiration_seconds:
                # self.logger.info(
                #    f"Got cached_prices for {symbol}, prices: {cached_prices}")
                return cached_prices
            else:
                del self._prices_cache[prices_cache_key]

        try:
            cache_key = f"{symbol}_{interval}"

            # Get klines data with double the lookback period
            klines = await self.client.get_klines(
                symbol=symbol,
                interval=interval,
                limit=lookback_periods+1
            )

            # Create dictionary of timestamp -> kline for new data
            new_klines_dict = {kline[0]: kline for kline in klines[:-1]}

            if cache_key in self._klines_cache:
                cached_klines = self._klines_cache[cache_key]

                # Check for conflicts
                for timestamp, kline in new_klines_dict.items():
                    if timestamp in cached_klines:
                        cached_kline = cached_klines[timestamp]
                        if cached_kline[4] != kline[4]:  # Compare closing prices
                            msg = f"Price conflict detected for {symbol} at timestamp {timestamp}: " \
                                f"cached={cached_kline[4]}, new={kline[4]}"
                            self.logger.warning(msg)
                            raise Exception(msg)

            # Update cache with new data
            self._klines_cache[cache_key] = new_klines_dict

            # Extract the required number of closing prices
            # cut off the last one to ensure that the prices we use are complete
            prices = [Decimal(kline[4]) for kline in klines[:-1]]
            self._prices_cache[prices_cache_key] = (prices, time.time())
            self.logger.info(
                f"Got historical prices for {symbol}, prices: {prices}")
            return prices

        except Exception as e:
            self.logger.error(
                f"Error getting historical prices for {symbol}: {e}")
            return []

    async def get_current_price(self, symbol: str) -> Optional[Decimal]:
        """
        Get current price for a symbol
        
        Args:
            symbol: Trading pair symbol (e.g. 'BTCUSDT')
            
        Returns:
            Current price as Decimal, or None if price cannot be retrieved
        """
        try:
            ticker = await self.get_ticker(symbol)
            return ticker.lastPrice
        except Exception as e:
            self.logger.error(f"Error getting current price for {symbol}: {e}")
            return None
