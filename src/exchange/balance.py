from dataclasses import dataclass
from typing import Dict, Any, Optional
from datetime import datetime
from decimal import Decimal


@dataclass
class AssetBalance:
    """Represents balance information for a single asset"""
    asset: str
    free: Decimal
    locked: Decimal

    @classmethod
    def from_binance_response(cls, response: Dict[str, Any]) -> 'AssetBalance':
        return cls(
            asset=response['asset'],
            free=Decimal(str(response['free'])),
            locked=Decimal(str(response['locked']))
        )

@dataclass
class Balance:
    """
    Represents the full account balance following Binance API structure
    """
    balances: Dict[str, AssetBalance]  # Asset symbol to balance mapping
    updateTime: int  # Last update time
    accountType: str  # Account type (e.g., SPOT)
    permissions: list  # List of permissions
    info: Dict[str, Any]  # Original raw response

    @classmethod
    def from_binance_response(cls, response: Dict[str, Any]) -> 'Balance':
        """Create Balance instance from Binance API response"""
        balances = {
            item['asset']: AssetBalance.from_binance_response(item)
            for item in response['balances']
        }

        return cls(
            balances=balances,
            updateTime=response.get('updateTime', int(datetime.now().timestamp() * 1000)),
            accountType=response.get('accountType', 'SPOT'),
            permissions=response.get('permissions', ['SPOT']),
            info=response
        ) 