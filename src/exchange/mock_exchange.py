import random
from typing import Dict, Any, List, Optional, Callable, Tuple
from decimal import Decimal, ROUND_DOWN, InvalidOperation
import uuid
import pandas as pd
import numpy as np
import os
import json
from datetime import datetime, timedelta
import asyncio
import aiohttp
import time
from .binance import BinanceExchange
from .base_exchange import BaseExchange
from .execution_report import ExecutionReport
from .balance import Balance, AssetBalance
from .ticker import Ticker
from .trade import Trade
from config import Config
from utils.logging import Logger

class MockExchange(BaseExchange):
    def __init__(self, api_key: str, secret_key: str):
        super().__init__()
        self.logger = Logger.get_logger()
        self._api_key = api_key
        self._secret_key = secret_key

        # Initialize balances from config
        self.balances = {}
        for coin, amount in Config.BACKTEST_INITIAL_BALANCE.items():
            self.balances[coin] = AssetBalance(
                asset=coin,
                free=Decimal(str(amount)),
                locked=Decimal('0')
            )

        # Initialize price data cache with size limits
        self.price_data = {}
        self.price_cache = {}
        self._max_price_cache_size = 200  # Limit cache size
        self._max_price_data_size = 200     # Limit number of symbols in price_data

        # Initialize virtual time
        self.current_time = Config.get_backtest_start_time()
        self.end_time = Config.get_backtest_end_time()

        # Initialize order book
        self.orders = {}

        # Initialize order callback
        self.order_update_callback = None

        # Create data directory
        self.data_dir = os.path.join(os.path.dirname(__file__), '..', '..', 'data')
        os.makedirs(self.data_dir, exist_ok=True)

        self.logger.info(f"MockExchange initialized with balances: {self.balances}")
        self.logger.info(f"Backtest period: {self.current_time} to {self.end_time}")

    def _clean_caches(self):
        """Clean up caches to prevent memory leaks"""
        # Clean price_cache if it gets too large
        if len(self.price_cache) > self._max_price_cache_size:
            self.logger.info(
                f"!!!!!!!!price_cache size: {len(self.price_cache)}")
            # Keep only the most recent entries
            keys_to_remove = sorted(self.price_cache.keys())[
                :-self._max_price_cache_size//2]
            for key in keys_to_remove:
                del self.price_cache[key]
            self.logger.info(
                f"Cleaned price_cache, removed {len(keys_to_remove)} entries")

        # Clean price_data if it gets too large
        if len(self.price_data) > self._max_price_data_size:
            self.logger.info(
                f"!!!!!!!!price_data size: {len(self.price_data)}")
            # Remove least recently used entries
            #keys_to_remove = list(self.price_data.keys())[
            #    :-self._max_price_data_size]
            #for key in keys_to_remove:
            #    del self.price_data[key]
            #self.logger.debug(
            #    f"Cleaned price_data, removed {len(keys_to_remove)} entries")

    async def get_exchange_info(self) -> Dict[str, Any]:
        """Get exchange information"""
        binance_exchange = BinanceExchange(
            api_key=Config.EXCHANGE_API_KEY,
            secret_key=Config.EXCHANGE_SECRET_KEY
        )
        await binance_exchange.start()
        exchange_info = await binance_exchange.get_exchange_info()
        await binance_exchange.stop()
        return exchange_info

    async def fetch_klines_from_binance(self, symbol: str, start_time: datetime, end_time: datetime, interval: str = '1m'):
        """Fetch historical klines (candlestick) data from Binance API"""
        self.logger.info(
            f"Fetching historical data for {symbol} from {start_time} to {end_time} interval: {interval}")

        # Convert times to milliseconds timestamp
        start_ms = int(start_time.timestamp() * 1000)
        end_ms = int(end_time.timestamp() * 1000)

        # Binance API endpoint
        url = "https://api.binance.com/api/v3/klines"

        all_klines = []
        current_start = start_ms

        # Binance limits to 1000 candles per request, so we may need multiple requests
        while current_start < end_ms:
            params = {
                "symbol": symbol,
                "interval": interval,
                "startTime": current_start,
                "endTime": end_ms,
                "limit": 1000
            }

            try:
                async with aiohttp.ClientSession() as session:
                    async with session.get(url, params=params) as response:
                        if response.status == 200:
                            klines = await response.json()
                            if not klines:
                                break

                            all_klines.extend(klines)

                            # Update start time for next batch
                            current_start = klines[-1][0] + 1
                        else:
                            error_text = await response.text()
                            self.logger.error(f"Error fetching klines: {response.status} - {error_text}")
                            # Add a delay to avoid rate limits
                            await asyncio.sleep(1)
                            break
            except Exception as e:
                self.logger.error(f"Exception fetching klines: {e}")
                await asyncio.sleep(1)
                break

        # Convert to DataFrame
        if all_klines:
            df = pd.DataFrame(all_klines, columns=[
                'timestamp', 'open', 'high', 'low', 'close', 'volume',
                'close_time', 'quote_asset_volume', 'number_of_trades',
                'taker_buy_base_asset_volume', 'taker_buy_quote_asset_volume', 'ignore'
            ])

            # Convert types
            df['timestamp'] = pd.to_datetime(df['timestamp'], unit='ms')
            for col in ['open', 'high', 'low', 'close', 'volume']:
                df[col] = df[col].astype(float)

            # Save to cache file
            file_path = os.path.join(self.data_dir, f"{symbol}_{interval}_{start_time.strftime('%Y%m%d')}_{end_time.strftime('%Y%m%d')}.csv")
            df.to_csv(file_path, index=False)

            return df

        return pd.DataFrame()

    async def get_historical_data_by_months(self, symbol: str, interval: str = '15m'):
        """Get historical data for a symbol split by months"""
        cache_key = f"{symbol}_{interval}"

        # if symbol == 'BTCUSDT':
        #    print("for debug")

        # If already in memory, return it
        if cache_key in self.price_data:
            return self.price_data[cache_key]

        start_time = Config.get_backtest_start_time()
        end_time = Config.get_backtest_end_time()

        all_data = []
        current_date = start_time.replace(day=1)  # Start at beginning of month

        # Limit the number of months to process to prevent excessive memory usage
        max_months = 12  # Reasonable limit for most backtests
        month_count = 0

        while current_date <= end_time and month_count < max_months:
            month_count += 1
            # Calculate end of month
            if current_date.month == 12:
                next_month = current_date.replace(
                    year=current_date.year + 1, month=1)
            else:
                next_month = current_date.replace(month=current_date.month + 1)

            month_end = min(next_month - timedelta(seconds=1), end_time)

            # Check for cached monthly data file
            monthly_file = os.path.join(
                self.data_dir,
                f"{symbol}_{interval}_{current_date.strftime('%Y%m')}.csv"
            )

            if os.path.exists(monthly_file):
                self.logger.info(
                    f"Loading cached monthly data for {symbol} from {monthly_file}")
                try:
                    df_month = pd.read_csv(
                        monthly_file, parse_dates=['timestamp'])
                    if not df_month.empty:
                        all_data.append(df_month)
                except Exception as e:
                    self.logger.error(f"Error loading cached data: {e}")
                    # If file is corrupted, try to fetch again
                    os.remove(monthly_file)
                    df_month = await self.fetch_klines_from_binance(
                        symbol,
                        current_date,
                        month_end,
                        interval
                    )
                    if not df_month.empty:
                        df_month.to_csv(monthly_file, index=False)
                        all_data.append(df_month)
            else:
                self.logger.info(
                    f"Fetching data for {symbol} for {current_date.strftime('%Y-%m')}")
                df_month = await self.fetch_klines_from_binance(
                    symbol,
                    current_date,
                    month_end,
                    interval
                )
                if not df_month.empty:
                    df_month.to_csv(monthly_file, index=False)
                    all_data.append(df_month)

            current_date = next_month

        if all_data:
            df = pd.concat(all_data, ignore_index=True)
            # Filter to exact date range needed
            df = df[(df['timestamp'] >= start_time)
                    & (df['timestamp'] <= end_time)]

            # Store in cache but check cache size first
            if len(self.price_data) >= self._max_price_data_size:
                self.logger.info(
                    f"!!!!!price_data size: {len(self.price_data)}")
                # Remove oldest entry
                # oldest_key = next(iter(self.price_data))
                # del self.price_data[oldest_key]

            self.price_data[cache_key] = df
            return df

        self.logger.error(f"Could not fetch data for {symbol}")
        return pd.DataFrame()


    async def advance_time(self, seconds: int = None):
        """Advance the virtual time by specified seconds or use config time step"""
        if seconds is None:
            seconds = Config.BACKTEST_TIME_STEP
        self.current_time += timedelta(seconds=seconds)
        self.logger.info(
            f"Advancing time by {seconds} seconds to: {self.current_time}")
        if self.current_time > self.end_time:
            self.current_time = self.end_time
            return False  # Indicates we've reached the end of the backtest period

        # Process any pending orders that would have been filled by this time
        await self.process_pending_orders()

        # Clean caches periodically to prevent memory leaks
        if random.random() < 0.05:  # ~5% chance each time step
            self._clean_caches()

        return True  # Indicates we can continue

    async def process_pending_orders(self):
        """Process any pending orders based on current price data"""
        for order_id, order in list(self.orders.items()):
            if order['status'] == 'NEW':
                symbol = order['symbol']
                side = order['side']
                order_type = order['type']

                # Get current price for the symbol
                current_price = await self.get_current_price(symbol)

                # Skip if no valid price
                if current_price <= Decimal('0'):
                    self.logger.warning(f"No valid price for {symbol}, skipping order {order_id}")
                    continue

                # For market orders, execute immediately (should have been executed at creation)
                if order_type == 'MARKET':
                    self.logger.info(f"Processing pending market order {order_id}")
                    await self.fill_order(order_id, current_price)

                # For limit orders, check if price conditions are met
                elif order_type == 'LIMIT':
                    limit_price = Decimal(str(order['price']))

                    # For BUY orders, execute if current price is less than or equal to limit price
                    # For SELL orders, execute if current price is greater than or equal to limit price
                    if (side.lower() == 'buy' and current_price <= limit_price) or \
                       (side.lower() == 'sell' and current_price >= limit_price):
                        self.logger.info(f"Limit order {order_id} conditions met. Executing at {limit_price}")
                        await self.fill_order(order_id, limit_price)

    async def get_current_price(self, symbol: str) -> Decimal:
        """Get the current price for a symbol based on the virtual timestamp"""
        # Get historical data for the symbol
        df = await self.get_historical_data_by_months(symbol, '1m')

        if df.empty:
            self.unrecoverable_error(f"No price data available for {symbol}")
            return Decimal('0')

        # Find the closest timestamp
        closest_idx = df['timestamp'].searchsorted(self.current_time)
        if closest_idx >= len(df):
            closest_idx = len(df) - 1

        # Get the price at that timestamp
        price = df.iloc[closest_idx]['close']

        # Cache the price for this timestamp but use a more efficient caching strategy
        # Minute-level granularity
        cache_key = f"{symbol}_{self.current_time.strftime('%Y%m%d%H%M')}"
        self.price_cache[cache_key] = Decimal(str(price))

        # Clean cache if it gets too large
        if len(self.price_cache) > self._max_price_cache_size:
            self._clean_caches()

        return Decimal(str(price))

    async def fill_order(self, order_id: str, price: Decimal):
        """Fill an order at the specified price"""
        order = self.orders[order_id]
        symbol = order['symbol']
        side = order['side']

        base_asset, quote_asset = self.split_symbol(symbol)

        # Calculate executed quantity and cost
        if 'quantity' in order and order['quantity']:
            # Order specified a base asset quantity
            executed_qty = Decimal(str(order['quantity']))
            cost = (executed_qty * price).quantize(Decimal('0.00000001'),
                                                   rounding=ROUND_DOWN)
        else:  # quoteOrderQty
            # Order specified a quote asset quantity
            cost = Decimal(str(order['quoteOrderQty']))
            # Calculate base asset quantity and round down to 8 decimal places
            executed_qty = (
                cost / price).quantize(Decimal('0.00000001'), rounding=ROUND_DOWN)

        # Calculate trading fee
        fee_rate = Decimal(str(Config.BACKTEST_TRADING_FEE))

        # Update balances
        if side.lower() == 'buy':
            # For BUY orders, fee is paid in base asset
            base_fee = (
                executed_qty * fee_rate).quantize(Decimal('0.00000001'), rounding=ROUND_DOWN)
            net_base_received = (
                executed_qty - base_fee).quantize(Decimal('0.00000001'), rounding=ROUND_DOWN)

            # Deduct quote asset (what we're paying with)
            if quote_asset in self.balances:
                if self.balances[quote_asset].free < cost:
                    self.logger.warning(f"Insufficient {quote_asset} balance for order {order_id}. Required: {cost}, Available: {self.balances[quote_asset].free}")
                    # Update order status to REJECTED
                    order['status'] = 'REJECTED'
                    order['executedQty'] = '0'
                    order['cummulativeQuoteQty'] = '0'
                    return
                self.balances[quote_asset].free -= cost
            else:
                self.logger.warning(f"No {quote_asset} balance found for order {order_id}")
                # Update order status to REJECTED
                order['status'] = 'REJECTED'
                order['executedQty'] = '0'
                order['cummulativeQuoteQty'] = '0'
                return

            # Add base asset minus fee
            if base_asset in self.balances:
                self.balances[base_asset].free += net_base_received
            else:
                self.balances[base_asset] = AssetBalance(
                    asset=base_asset,
                    free=net_base_received,
                    locked=Decimal('0')
                )
        else:  # SELL
            # For SELL orders, fee is paid in quote asset
            quote_fee = (
                cost * fee_rate).quantize(Decimal('0.00000001'), rounding=ROUND_DOWN)
            net_quote_received = (
                cost - quote_fee).quantize(Decimal('0.00000001'), rounding=ROUND_DOWN)

            # Deduct base asset (what we're selling)
            if base_asset in self.balances:
                if self.balances[base_asset].free < executed_qty:
                    self.logger.warning(f"Insufficient {base_asset} balance for order {order_id}. Required: {executed_qty}, Available: {self.balances[base_asset].free}")
                    # Update order status to REJECTED
                    order['status'] = 'REJECTED'
                    order['executedQty'] = '0'
                    order['cummulativeQuoteQty'] = '0'
                    return
                self.balances[base_asset].free -= executed_qty
            else:
                self.logger.warning(f"No {base_asset} balance found for order {order_id}")
                # Update order status to REJECTED
                order['status'] = 'REJECTED'
                order['executedQty'] = '0'
                order['cummulativeQuoteQty'] = '0'
                return

            # Add quote asset minus fee
            if quote_asset in self.balances:
                self.balances[quote_asset].free += net_quote_received
            else:
                self.balances[quote_asset] = AssetBalance(
                    asset=quote_asset,
                    free=net_quote_received,
                    locked=Decimal('0')
                )

        # Update order status
        order['status'] = 'FILLED'
        order['executedQty'] = str(executed_qty)
        order['cummulativeQuoteQty'] = str(cost)

        # Record fee in the appropriate asset
        if side.lower() == 'buy':
            fee_amount = base_fee
            fee_asset = base_asset
        else:  # SELL
            fee_amount = quote_fee
            fee_asset = quote_asset

        order['fills'] = [{
            'price': str(price),
            'qty': str(executed_qty),
            'commission': str(fee_amount),
            'commissionAsset': fee_asset
        }]

        # Create execution report
        execution_report = ExecutionReport(
            id=int(order_id.split('-')[1]),
            clientOrderId=order_id,
            symbol=symbol,
            side=side,
            time=int(self.current_time.timestamp() * 1000),
            status=ExecutionReport.STATUS_FILLED,
            info=order
        )

        self.logger.info(
            f"Order filled at {self.current_time} {order_id} {symbol} {side} {executed_qty} @ {price}, cost: {cost}, fee: {fee_amount} {fee_asset}, balance quote: {self.balances[quote_asset].free} balance base: {self.balances[base_asset].free}")

        # Call order update callback
        if self.order_update_callback:
            await self.order_update_callback(execution_report)


    async def start(self):
        """Start the exchange connection"""
        self.logger.info("Starting MockExchange...")
        # Nothing to do for mock exchange
        pass

    async def stop(self):
        """Stop the exchange connection"""
        self.logger.info("Stopping MockExchange...")
        # Clear caches to free memory
        self.price_data.clear()
        self.price_cache.clear()
        self.orders.clear()

    async def get_ticker(self, symbol: str) -> Ticker:
        """Get ticker information"""
        current_price = await self.get_current_price(symbol)

        # Create a synthetic ticker with the current price
        ticker_data = {
            'symbol': symbol,
            'priceChange': '0',
            'priceChangePercent': '0',
            'weightedAvgPrice': str(current_price),
            'prevClosePrice': str(current_price),
            'lastPrice': str(current_price),
            'lastQty': '0',
            'bidPrice': str(current_price * Decimal('0.999')),
            'bidQty': '0',
            'askPrice': str(current_price * Decimal('1.001')),
            'askQty': '0',
            'openPrice': str(current_price),
            'highPrice': str(current_price * Decimal('1.01')),
            'lowPrice': str(current_price * Decimal('0.99')),
            'volume': '1000',
            'quoteVolume': str(current_price * Decimal('1000')),
            'openTime': int(self.current_time.timestamp() * 1000) - 86400000,
            'closeTime': int(self.current_time.timestamp() * 1000),
            'firstId': 1,
            'lastId': 100,
            'count': 100
        }

        return Ticker.from_binance_response(ticker_data)

    async def get_lot_size(self, symbol: str) -> Tuple[Decimal, Decimal, Decimal]:
        """Get lot size for a symbol

        Returns:
            Tuple of (min_qty, max_qty, step_size)
        """
        # Different step sizes for different symbols could be implemented here
        # For now, using fixed values for all symbols
        _ = symbol  # Acknowledge symbol parameter to avoid unused variable warning
        return Decimal('0.00000100'), Decimal('9000000'), Decimal('0.00000100')

    async def get_price_filter(self, symbol: str) -> Tuple[Decimal, Decimal, Decimal]:
        """Get price filter for a symbol

        Returns:
            Tuple of (min_price, max_price, tick_size)
        """
        # Different price filters for different symbols could be implemented here
        # For now, using fixed values for all symbols
        _ = symbol  # Acknowledge symbol parameter to avoid unused variable warning
        return Decimal('0.00000001'), Decimal('100000'), Decimal('0.00000001')

    async def create_order(
        self,
        symbol: str,
        type: str,
        side: str,
        amount: Decimal = None,
        quote_amount: Decimal = None,
        price: Decimal = None,
        client_order_id: str = None,
    ) -> str:
        """Create a new order"""
        self.logger.info(f"Creating order: symbol: {symbol} type: {type} side: {side} amount: {amount} quote_amount: {quote_amount} price: {price} client_order_id: {client_order_id}")

        # Validate order parameters
        type = type.upper()
        side = side.upper()

        # Check required parameters
        if type == 'LIMIT' and price is None:
            self.logger.error(f"Price is required for LIMIT orders")
            raise ValueError("Price is required for LIMIT orders")

        if amount is None and quote_amount is None:
            self.logger.error(f"Either amount or quote_amount must be provided")
            raise ValueError("Either amount or quote_amount must be provided")

        # Generate order ID if not provided
        if not client_order_id:
            client_order_id = f"mock-{uuid.uuid4().int % 10000000}"

        # Get step size for quantity rounding and price filter for price rounding
        _, _, step_size = await self.get_lot_size(symbol)
        _, _, tick_size = await self.get_price_filter(symbol)


        # Extract base and quote assets from symbol
        base_asset, quote_asset = self.split_symbol(symbol)

        # Validate balances for the order
        if side.lower() == 'buy':
            # For BUY orders, check if we have enough quote asset
            if quote_asset not in self.balances:
                self.logger.warning(f"No {quote_asset} balance found for order")
            elif type == 'MARKET' and quote_amount is not None:
                # For market orders with quote amount, check if we have enough quote asset
                if self.balances[quote_asset].free < quote_amount:
                    self.logger.warning(f"Insufficient {quote_asset} balance for order. Required: {quote_amount}, Available: {self.balances[quote_asset].free}")
            elif amount is not None and price is not None:
                # For limit orders or market orders with base amount, calculate quote amount needed
                quote_needed = amount * price
                if self.balances[quote_asset].free < quote_needed:
                    self.logger.warning(f"Insufficient {quote_asset} balance for order. Required: {quote_needed}, Available: {self.balances[quote_asset].free}")
        else:  # SELL
            # For SELL orders, check if we have enough base asset
            if base_asset not in self.balances:
                self.logger.warning(f"No {base_asset} balance found for order")
            elif amount is not None:
                # Check if we have enough base asset
                if self.balances[base_asset].free < amount:
                    self.logger.warning(f"Insufficient {base_asset} balance for order. Required: {amount}, Available: {self.balances[base_asset].free}")
            elif quote_amount is not None and price is not None:
                # Calculate base amount needed for the quote amount
                base_needed = (
                    quote_amount / price).quantize(Decimal('0.00000001'), rounding=ROUND_DOWN)
                if self.balances[base_asset].free < base_needed:
                    self.logger.warning(f"Insufficient {base_asset} balance for order. Required: {base_needed}, Available: {self.balances[base_asset].free}")

        # Create order object
        order = {
            'symbol': symbol,
            'orderId': int(client_order_id.split('-')[1]),
            'clientOrderId': client_order_id,
            'transactTime': int(self.current_time.timestamp() * 1000),
            'price': str(price) if price else '0',
            'origQty': '0',
            'executedQty': '0',
            'cummulativeQuoteQty': '0',
            'status': 'NEW',
            'timeInForce': 'GTC' if type == 'LIMIT' else None,
            'type': type,
            'side': side,
            'fills': []
        }

        # Set quantity or quoteOrderQty
        if amount is not None:
            # Round amount to step size
            order['quantity'] = str(amount)
            order['origQty'] = str(amount)
        elif quote_amount is not None:
            order['quoteOrderQty'] = str(quote_amount)

            # For market orders with quote amount, calculate and set the base amount as well
            if type == 'MARKET':
                # Get current price for the symbol
                current_price = await self.get_current_price(symbol)
                if current_price > Decimal('0'):
                    # Calculate base amount and round to step size
                    base_amount = (quote_amount / current_price).quantize(
                        Decimal('0.00000001'), rounding=ROUND_DOWN)
                    # Set as origQty for reference
                    order['origQty'] = str(base_amount)

        # Store the order
        self.orders[client_order_id] = order

        # For market orders, CAN NOT process immediately, it would cause deadlock

        return client_order_id

    async def get_trade(self, symbol: str, id: int) -> Trade:
        """Get trade information"""
        # Find the order by ID
        order_id = None
        for oid, order in self.orders.items():
            if order['orderId'] == id:
                order_id = oid
                break

        if not order_id or self.orders[order_id]['status'] != 'FILLED':
            return None

        order = self.orders[order_id]

        # Create trade object
        trade = Trade(
            symbol=symbol,
            orderId=id,
            qty=Decimal(order['executedQty']),
            quoteQty=Decimal(order['cummulativeQuoteQty']),
            commission=Decimal(order['fills'][0]['commission']),
            commissionAsset=order['fills'][0]['commissionAsset'],
            commissions={order['fills'][0]['commissionAsset']: Decimal(order['fills'][0]['commission'])},
            price=Decimal(order['fills'][0]['price'])
        )

        return trade

    async def get_order_status(self, symbol: str, client_order_id: str) -> ExecutionReport:
        """Get order status by client order ID"""
        if client_order_id not in self.orders:
            self.logger.warning(f"Order {client_order_id} not found")
            return None

        order = self.orders[client_order_id]
        if order['symbol'] != symbol:
            self.logger.warning(
                f"Order {client_order_id} symbol mismatch: {order['symbol']} != {symbol}")
            return None

        # Create execution report from order data
        execution_report = ExecutionReport(
            id=str(order['orderId']),
            symbol=order['symbol'],
            status=order['status'].lower(),
            clientOrderId=order['clientOrderId'],
            time=order['transactTime'],
            info=order,
            side=order['side'].lower()
        )

        return execution_report

    async def get_balance(self, currency: str = None) -> Balance:
        """Get account balance"""
        # Create balance response
        balance_data = {
            'balances': [],
            'updateTime': int(self.current_time.timestamp() * 1000),
            'accountType': 'SPOT',
            'permissions': ['SPOT']
        }

        # Add all balances or just the requested currency
        if currency:
            if currency in self.balances:
                balance_data['balances'].append({
                    'asset': currency,
                    'free': str(self.balances[currency].free),
                    'locked': str(self.balances[currency].locked)
                })
        else:
            for asset, balance in self.balances.items():
                balance_data['balances'].append({
                    'asset': asset,
                    'free': str(balance.free),
                    'locked': str(balance.locked)
                })

        return Balance.from_binance_response(balance_data)

    def set_order_update_callback(self, callback: Callable[[ExecutionReport], None]):
        """Set callback for all order updates"""
        self.order_update_callback = callback

    async def get_historical_prices(self, symbol: str, lookback_periods: int, interval: str = '30m') -> List[Decimal]:
        """
        Get historical closing prices for a symbol
        
        Args:
            symbol: Trading pair symbol
            lookback_periods: Number of periods to look back
            interval: Kline interval (e.g. '1m', '1h', '1d')
            
        Returns:
            List of historical prices
        """
        # Get historical data
        df = await self.get_historical_data_by_months(symbol, interval)

        if df.empty:
            self.logger.error(f"No historical data available for {symbol}")
            return []

        # Find the current timestamp index
        current_idx = df['timestamp'].searchsorted(self.current_time)
        if current_idx >= len(df):
            current_idx = len(df) - 1

        # Calculate start index with bounds checking
        start_idx = max(0, current_idx - lookback_periods)

        # Extract the relevant slice of data
        prices_df = df.iloc[start_idx:current_idx+1]

        # Convert to list of Decimals
        prices = [Decimal(str(price)) for price in prices_df['close']]

        # Ensure we have exactly lookback_periods prices
        if len(prices) < lookback_periods:
            # Pad with the earliest price if we don't have enough data
            padding = [prices[0]] * (lookback_periods - len(prices))
            prices = padding + prices
        elif len(prices) > lookback_periods:
            # Take only the most recent prices
            prices = prices[-lookback_periods:]

        return prices
