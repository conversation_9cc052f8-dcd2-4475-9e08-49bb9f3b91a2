from dataclasses import dataclass
from typing import Optional, Dict, Any
from datetime import datetime
from decimal import Decimal

@dataclass
class ExecutionReport:
    """Represents a trading order following Binance API structure"""
    id: str  # orderId
    symbol: str  # symbol
    status: str  # status
    clientOrderId: str  # clientOrderId
    time: int  # transaction time
    info: Dict[str, Any]  # raw response
    side: str  # side
    
    # https://developers.binance.com/docs/binance-spot-api-docs/enums#order-status-status
    STATUS_FILLED = 'filled'
    STATUS_PARTIALLY_FILLED = 'partially_filled'
    STATUS_CANCELED = 'canceled'
    STATUS_NEW = 'new'
    STATUS_REJECTED = 'rejected'
    
    @classmethod
    def from_binance_response(cls, response: Dict[str, Any]) -> 'ExecutionReport':
        """Create Order instance from Binance API response"""
        if 'e' in response and response['e'] == 'executionReport':  # WebSocket response
            return cls(
                id=str(response['i']),  # orderId
                symbol=response['s'],  # symbol
                status=response['X'].lower(),  # status
                clientOrderId=response['c'],  # clientOrderId
                time=response['T'],  # transactTime
                info=response,
                side=response['S'].lower()  # side
            )
