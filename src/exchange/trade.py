from dataclasses import dataclass
from typing import Optional, Dict, Any
from datetime import datetime
from decimal import Decimal

@dataclass
class Trade:
    # https://developers.binance.com/docs/binance-spot-api-docs/testnet/rest-api/account-endpoints#account-trade-list-user_data
    symbol: str
    orderId: int
    qty: Decimal
    quoteQty: Decimal
    # commission & commissionAsset may not exists if commissions is not empty
    commission: Decimal
    commissionAsset: str
    commissions: Dict[str, Decimal]
    price: Decimal

    @classmethod
    def from_binance_response(cls, response: Dict[str, Any]) -> 'Trade':
        return cls(
            symbol=response['symbol'],
            orderId=response['orderId'],
            qty=Decimal(response['qty']),
            commission=Decimal(response['commission'])
            if 'commission' in response else None,
            quoteQty=Decimal(response['quoteQty']),
            commissionAsset=response['commissionAsset']
            if 'commissionAsset' in response else None,
            commissions=response['commissions']
            if 'commissions' in response else {},
            price=Decimal(response['price'])
        ) 
