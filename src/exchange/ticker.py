from dataclasses import dataclass
from typing import Dict, Any
from decimal import Decimal

@dataclass
class Ticker:
    """
    Represents a price ticker following Binance API structure
    """
    symbol: str          # Trading pair symbol
    priceChange: Decimal   # Price change
    priceChangePercent: Decimal  # Price change percent
    weightedAvgPrice: Decimal    # Weighted average price
    prevClosePrice: Decimal      # Previous close price
    lastPrice: Decimal          # Last price
    lastQty: Decimal           # Last quantity
    bidPrice: Decimal          # Best bid price
    bidQty: Decimal           # Best bid quantity
    askPrice: Decimal          # Best ask price
    askQty: Decimal           # Best ask quantity
    openPrice: Decimal         # Open price
    highPrice: Decimal         # High price
    lowPrice: Decimal          # Low price
    volume: Decimal           # Total traded base asset volume
    quoteVolume: Decimal      # Total traded quote asset volume
    openTime: int           # Open time
    closeTime: int          # Close time
    firstId: int           # First trade ID
    lastId: int            # Last trade ID
    count: int             # Total number of trades
    info: Dict[str, Any]   # Raw response

    @classmethod
    def from_binance_response(cls, response: Dict[str, Any]) -> 'Ticker':
        """Create Ticker instance from Binance API response"""
        return cls(
            symbol=response['symbol'],
            priceChange=Decimal(str(response['priceChange'])),
            priceChangePercent=Decimal(str(response['priceChangePercent'])),
            weightedAvgPrice=Decimal(str(response['weightedAvgPrice'])),
            prevClosePrice=Decimal(str(response['prevClosePrice'])),
            lastPrice=Decimal(str(response['lastPrice'])),
            lastQty=Decimal(str(response['lastQty'])),
            bidPrice=Decimal(str(response['bidPrice'])),
            bidQty=Decimal(str(response['bidQty'])),
            askPrice=Decimal(str(response['askPrice'])),
            askQty=Decimal(str(response['askQty'])),
            openPrice=Decimal(str(response['openPrice'])),
            highPrice=Decimal(str(response['highPrice'])),
            lowPrice=Decimal(str(response['lowPrice'])),
            volume=Decimal(str(response['volume'])),
            quoteVolume=Decimal(str(response['quoteVolume'])),
            openTime=response['openTime'],
            closeTime=response['closeTime'],
            firstId=response['firstId'],
            lastId=response['lastId'],
            count=response['count'],
            info=response
        ) 