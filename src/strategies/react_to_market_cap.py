import traceback
import aiohttp
import asyncio
from typing import Dict, Any, List, Optional
from decimal import Decimal, ROUND_DOWN
from .base_strategy import BaseStrategy
from utils.telegram_client import TelegramClient
from exchange.base_exchange import BaseExchange
from exchange.execution_report import ExecutionReport
from exchange.binance import BinanceExchange
from config import Config
from db.trading import TradingDB
from .react_to_market_cap_config import ReactToMarketCapConfig
from utils.technical_indicators import TechnicalIndicators
from config import Config
from datetime import datetime

class ReactToMarketCapStrategy(BaseStrategy):
    def __init__(self, db: TradingDB, telegram: TelegramClient, exchange: BaseExchange = None):
        if exchange is None:
            exchange = BinanceExchange(
                api_key=Config.EXCHANGE_API_KEY,
                secret_key=Config.EXCHANGE_SECRET_KEY
            )

        super().__init__(exchange, telegram)
        self.db = db
        self.config = ReactToMarketCapConfig()
        self.coins_market_cap: Dict[str, Decimal] = {}
        self.prices_at_market_cap: Dict[str, Decimal] = {}

    async def load_market_caps(self):
        """Load market caps from CoinGecko API"""
        binance_exchange = self.exchange
        if Config.TRADING_MODE == 'backtest':
            binance_exchange = BinanceExchange(
                api_key=Config.EXCHANGE_API_KEY,
                secret_key=Config.EXCHANGE_SECRET_KEY
            )
        await binance_exchange.start()

        self.logger.info("Updating market caps...")
        try:
            async with aiohttp.ClientSession() as session:
                params = {
                    'vs_currency': 'usd',
                    'order': 'market_cap_desc',
                    'per_page': 250,  # Get more coins to ensure we cover all trading coins
                    'page': 1
                }

                async with session.get(self.config.COINGECKO_API_URL, params=params) as response:
                    self.logger.info(
                        f"Got market caps response, status: {response.status}")
                    if response.status == 200:
                        data = await response.json()
                        # Update market caps
                        trading_coins = self.get_all_trading_coins()
                        self.coins_market_cap = {
                            coin['symbol'].upper(): Decimal(str(coin['market_cap']))
                            for coin in data
                            if coin['symbol'].upper() in trading_coins
                        }
                        self.logger.info(
                            f"Updated market caps for {len(self.coins_market_cap)} coins")
                    elif response.status == 429:
                        self.logger.warning(
                            "Update market caps encounter rate limited, waiting 2 seconds...")
                        await asyncio.sleep(2)
                        return await self.load_market_caps()  # Retry
                    else:
                        await self.unrecoverable_error(
                            f"Failed to get market caps: {response.status}")
                        return

                # Use binance to update the prices at the same time as the market cap
                for coin in trading_coins:
                    # Get current price from exchange
                    try:
                        price = await binance_exchange.get_current_price(coin.upper() + "USDT")
                        if price:
                            self.prices_at_market_cap[coin] = price
                    except Exception as e:
                        self.logger.error(
                            f"Error getting real-time price for {coin}: {e}")

            self.logger.info("Market caps updated")

            # If in backtest mode, close the exchange to prevent connection leaks
            # if Config.TRADING_MODE == 'backtest' and binance_exchange != self.exchange:
            #    await binance_exchange.stop()

        except Exception as e:
            self.logger.error(f"Error updating market caps: {e}")
            self.logger.error(traceback.format_exc())

            # If in backtest mode, close the exchange to prevent connection leaks
            # if Config.TRADING_MODE == 'backtest' and binance_exchange != self.exchange:
            #    await binance_exchange.stop()

    def get_all_trading_coins(self) -> set[str]:
        """Get all coins involved in trading"""
        coins = set()
        for coin in self.config.TRADING_COINS.keys():
            coins.add(coin)
        coins.add(self.config.INITIAL_COIN)
        return coins

    async def sell_all_non_btc(self):
        balance = await self.exchange.get_balance()
        for coin in balance.balances.keys():
            if coin == 'BNB':
                continue
            if coin != "BTC" and balance.balances[coin].free > 0.01:
                self.logger.info(
                    f"Selling {coin}:{balance.balances[coin].free}...")
                await self.exchange.create_order(
                    symbol=coin + "BTC",
                    type='market',
                    side='sell',
                    amount=balance.balances[coin].free
                )

    async def initialize_balances(self):
        """Initialize trading balances if none exist"""
        current_balances = await self.db.get_react_to_market_cap_all_current_balances()
        
        if not current_balances:
            # Get initial balance
            balance = await self.exchange.get_balance(self.config.INITIAL_COIN)
            initial_amount = balance.balances[self.config.INITIAL_COIN].free
            
            if initial_amount <= 0:
                raise ValueError(f"No balance found for initial coin {self.config.INITIAL_COIN}")

            cnt_coins = 0
            for coin in self.config.TRADING_COINS.keys():
                cnt_coins += len(self.config.TRADING_COINS[coin])

            # Calculate amount per pair
            amount_per_pair = (initial_amount / cnt_coins).quantize(
                Decimal('0.00000001'), rounding=ROUND_DOWN)

            msg = f"Initializing with {amount_per_pair} {self.config.INITIAL_COIN} per pair, cnt_coins:{cnt_coins}"
            self.logger.info(msg)
            await self.telegram.send_message(msg)
                
            # Create balance entries for each pair
            for coin in self.config.TRADING_COINS.keys():
                pair = coin + self.config.INITIAL_COIN
                for config in self.config.TRADING_COINS[coin]:
                    await self.db.update_react_to_market_cap_current_balance(
                        pair=pair,
                        long_term=config.long_term,
                        short_term=config.short_term,
                        amount=amount_per_pair
                    )

    async def calculate_historical_market_caps(self, lookback_periods: int, interval: str = '1h') -> List[Decimal]:
        coins = [self.config.INITIAL_COIN] + \
            list(self.config.TRADING_COINS.keys())
        sum_market_caps = []
        # Get historical prices
        for coin in coins:
            prices = await self.exchange.get_historical_prices(coin.upper()+"USDT", lookback_periods, interval)
            if not prices or len(prices) != lookback_periods:
                self.logger.warning(
                    f"coin:{coin} invalid get_historical_prices, prices:{prices}")
                return
            #strprices = ", ".join(f"{p}" for p in prices)
            #print(f"coin:{coin} prices:{strprices}")

            # Get current market cap
            market_cap = self.coins_market_cap.get(coin)
            if not market_cap:
                continue

            # Calculate historical market caps assuming constant volume
            for i, price in enumerate(prices):
                # Calculate market cap ratio based on price ratio
                market_cap_ratio = price / self.prices_at_market_cap[coin]
                historical_market_cap = market_cap * market_cap_ratio
                # add up the market caps
                if len(sum_market_caps) <= i:
                    sum_market_caps.append(historical_market_cap)
                else:
                    sum_market_caps[i] += historical_market_cap
        #print("sum_market_caps" + ":" +
        #      ",".join(f"{s}" for s in sum_market_caps))
        return sum_market_caps

    async def check_pending_orders(self):
        """Check for any pending orders that might have been missed during application restart"""
        self.logger.info("Checking for pending orders...")

        # Get all in-flight balances
        current_balances = await self.db.get_react_to_market_cap_all_current_balances()
        in_flight_balances = [b for b in current_balances if b['in_flight']]

        if not in_flight_balances:
            self.logger.info("No pending orders found")
            return

        self.logger.info(f"Found {len(in_flight_balances)} pending orders")

        for balance in in_flight_balances:
            if not balance['order_id']:
                self.logger.warning(
                    f"Balance {balance['id']} marked as in_flight but has no order_id")
                await self.db.react_to_market_cap_current_balance_order_completed(
                    id=balance['id'],
                    amount=balance['amount'],
                    holding_base=balance['holding_base']
                )
                continue

            # Check order status
            order_id = balance['order_id']
            execution_report = await self.exchange.get_order_status(balance['pair'], order_id)

            if execution_report:
                self.logger.info(
                    f"Found order {order_id} with status {execution_report.status}")
                self.logger.info(
                    f"Processing missed order update for {order_id}")
                await self.handle_order_update(execution_report)
            else:
                self.logger.warning(
                    f"Could not retrieve status for order {order_id}")


    async def start(self):
        if self.config.TRADING_COINS is None or len(self.config.TRADING_COINS) == 0:
            raise ValueError(
                "TRADING_COINS is not set in environment variables")

        """Start the strategy"""
        self.logger.info("Starting React to Market Cap strategy...")
        await self.exchange.start()
        
        await self.load_market_caps()

        # Initialize balances if needed
        await self.initialize_balances()
        
        await self.check_pending_orders()


    async def stop(self):
        """Stop the strategy"""
        self.logger.info("Stopping strategy...")

        await self.exchange.stop()

    async def loop(self):
        # trade = await self.exchange.get_trade('HBARBTC', '283988371')
        # print(trade)
        # return
        """Main strategy loop"""
        if not self.coins_market_cap:
            self.logger.info("Waiting for market cap data...")
            return
            
        if not hasattr(self, '_loop_count'):
            self._loop_count = 0
        self._loop_count += 1
        if self._loop_count % 20 == 0:
            await self.check_pending_orders()

        current_balances = await self.db.get_react_to_market_cap_all_current_balances()
        for balance in current_balances:
            if balance['in_flight']:
                # Get or initialize in_flight_count from memory
                if not hasattr(self, '_in_flight_counts'):
                    self._in_flight_counts = {}

                balance_id = balance['id']
                self._in_flight_counts[balance_id] = self._in_flight_counts.get(
                    balance_id, 0) + 1

                # Check if exceeded maximum allowed continuous in-flight loops
                # 5 minutes with 1 second loop interval
                if self._in_flight_counts[balance_id] > 300:
                    await self.unrecoverable_error(f"Balance {balance_id} has been in flight for too long ({self._in_flight_counts[balance_id]} loops)")
                    return

                self.logger.info(
                    f"Current pair {balance['pair']} has orders in flight for {self._in_flight_counts[balance_id]} loops, skipping conversion")
                continue
            else:
                # Reset counter when not in flight
                if hasattr(self, '_in_flight_counts'):
                    self._in_flight_counts.pop(balance['id'], None)
            pair = balance['pair']
            long_term = balance['long_term']
            short_term = balance['short_term']
            
            # Calculate historical market caps
            long_market_caps = await self.calculate_historical_market_caps(long_term, self.config.EMA_LONG_INTERVAL)
            if not long_market_caps:
                self.logger.warning(
                    f"Failed to get historical long market caps, pair: {pair}")
                return

            # Calculate historical market caps
            short_market_caps = await self.calculate_historical_market_caps(short_term, self.config.EMA_SHORT_INTERVAL)
            if not short_market_caps:
                self.logger.warning(
                    f"Failed to get historical short market caps, pair: {pair}")
                return
                
            # Calculate EMAs
            long_ema = TechnicalIndicators.get_current_ema(
                long_market_caps, long_term)
            short_ema = TechnicalIndicators.get_current_ema(
                short_market_caps, short_term)
            diff = (short_ema - long_ema) / long_ema
            if diff > self.config.DEVIATION_THRESHOLD and not balance['holding_base']:
                last_price = balance['last_price']
                if last_price is not None:
                    current_price = await self.exchange.get_current_price(pair)
                    if current_price > last_price:
                        self.logger.info(
                            f"last_price:{last_price}, current_price:{current_price}, skipping buy")
                        continue

                # Buy signal
                self.logger.info(
                    f"Buy signal for balance, id: {balance['id']}, pair: {pair} long_term: {long_term}, short_term: {short_term} long_ema: {long_ema}, short_ema: {short_ema}")
                order_id = await self.exchange.create_order(
                    symbol=pair,
                    type='market',
                    side='buy',
                    quote_amount=balance['amount']
                )
                await self.db.react_to_market_cap_current_balance_order_placed(
                    id=balance['id'],
                    order_id=order_id
                )
                
            elif diff < -self.config.DEVIATION_THRESHOLD and balance['holding_base']:                    
                # Sell signal
                self.logger.info(
                    f"Sell signal for balance, id: {balance['id']}, pair: {pair} long_term: {long_term}, short_term: {short_term} long_ema: {long_ema}, short_ema: {short_ema}")
                order_id = await self.exchange.create_order(
                    symbol=pair,
                    type='market',
                    side='sell',
                    amount=balance['amount']
                )
                await self.db.react_to_market_cap_current_balance_order_placed(
                    id=balance['id'],
                    order_id=order_id
                )

    async def handle_order_update(self, execution_report: ExecutionReport):
        """Handle order updates"""
        if execution_report.status == ExecutionReport.STATUS_FILLED:
            # Find the balance entry for this order
            current_balances = await self.db.get_react_to_market_cap_all_current_balances()
            balance = next((b for b in current_balances if b['order_id'] == execution_report.clientOrderId), None)
            
            if balance:
                trade = await self.exchange.get_trade(execution_report.symbol, execution_report.id)
                if trade is None:
                    message = f"No trade found for order {execution_report.clientOrderId}"
                    await self.unrecoverable_error(message)
                    return

                # Calculate final amount
                final_amount = (
                    trade.qty if execution_report.side.lower() == 'buy' else trade.quoteQty)

                commissions = trade.commissions
                for commissionAsset, commission in commissions.items():
                    if commissionAsset == 'BNB':
                        await self.deduct_bnb_fee(commission)
                    else:
                        await self.telegram.send_message(
                            f"Deducted {commission} {commissionAsset} from balance, top up BNB!")
                        final_amount -= commission
                
                # Update balance
                await self.db.react_to_market_cap_current_balance_order_completed(
                    id=balance['id'],
                    amount=final_amount,
                    holding_base=(execution_report.side.lower() == 'buy'),
                    last_price=trade.price
                )
                
                msg = f"Order filled: {execution_report.symbol} {execution_report.side} " \
                      f"Final Amount: {final_amount}"
                self.logger.info(msg)
                await self.telegram.send_message(
                    msg
                )

    async def deduct_bnb_fee(self, fee: Decimal):
        self.logger.info(f"Deducted BNB fee from balance fee: {fee}")
        return


    async def get_status(self) -> List[str]:
        """Get status of the strategy"""
        status = []
        current_balances = await self.db.get_react_to_market_cap_all_current_balances()
        status.append(f"Name: React to Market Cap Strategy")
        status.append(f"Initial Coin: {self.config.INITIAL_COIN}")
        status.append(f"EMA Interval: {self.config.EMA_INTERVAL}")
        for balance in current_balances:
            status.append(f"Pair: {balance['pair']}, Long Term: {balance['long_term']}, Short Term: {balance['short_term']}, Amount: {balance['amount']}, Holding Base: {balance['holding_base']}, In Flight: {balance['in_flight']}")
        return status
