import traceback
from typing import Dict, Any, List, Optional
from decimal import Decimal, ROUND_DOWN
from collections import defaultdict
from .base_strategy import BaseStrategy
from .mean_reversion_config import MeanReversionConfig
from exchange.base_exchange import BaseExchange
from exchange.execution_report import ExecutionReport
from exchange.binance import BinanceExchange
from config import Config
from db.trading import TradingDB
from utils.telegram_client import TelegramClient
import sys

class MeanReversionStrategy(BaseStrategy):
    def __init__(self, db: TradingDB, telegram: TelegramClient, exchange: BaseExchange = None):
        if exchange is None:
            exchange = BinanceExchange(
                api_key=Config.EXCHANGE_API_KEY,
                secret_key=Config.EXCHANGE_SECRET_KEY
            )

        super().__init__(exchange, telegram)
        self.db = db
        self.config = MeanReversionConfig()
        self.trading_pairs = []
        self.pair_prices: Dict[str, Decimal] = {}
        self.bridge_prices: Dict[str, Decimal] = {}
        self.max_variations: Dict[int, str] = {}

    async def initialize_trading_pairs(self):
        """Initialize all possible trading pairs from configured coins"""
        exchange_info = await self.exchange.get_exchange_info()
        available_pairs = set(exchange_info.keys())
        unique_pairs = set()

        # Generate all possible combinations
        coins = self.config.TRADING_COINS + [self.config.INITIAL_COIN]
        for base in coins:
            for quote in coins:
                if base != quote:
                    direct_pair = f"{base}{quote}"
                    reverse_pair = f"{quote}{base}"
                    # check if direct pair or reverse pair exists
                    if direct_pair in unique_pairs or reverse_pair in unique_pairs:
                        continue
                    unique_pairs.add(direct_pair)

                    # Check if direct pair exists
                    if direct_pair in available_pairs:
                        self.trading_pairs.append({
                            "base": base,
                            "quote": quote,
                            "needs_bridge": False
                        })
                    # Check if reverse pair exists
                    elif reverse_pair in available_pairs:
                        self.trading_pairs.append({
                            "base": quote,
                            "quote": base,
                            "needs_bridge": False
                        })
                    else:
                        # If neither direct nor reverse pair exists, use bridge
                        self.trading_pairs.append({
                            "base": base,
                            "quote": quote,
                            "needs_bridge": True
                        })

        self.logger.info(
            f"Initialized {len(self.trading_pairs)} trading pairs, trading_pairs:{self.trading_pairs}")

    async def start(self):
        """Start the strategy"""
        self.logger.info("Starting Mean Reversion strategy...")
        await self.exchange.start()
        await self.initialize_trading_pairs()
        await self.update_all_prices()
        # Initialize balances if needed
        await self.initialize_balances()

    async def initialize_balances(self):
        # Try to restore previous state from database
        all_current_balances = await self.db.get_all_current_balances()
        if len(all_current_balances) == 0:
            # No previous state, get current balance from exchange
            initial_coin = self.config.INITIAL_COIN
            balance = await self.exchange.get_balance(initial_coin)
            initial_amount = balance.balances[initial_coin].free
            if initial_amount <= 0:
                raise ValueError(f"No balance found for initial coin {initial_coin}")

            self.logger.info(f"Starting with {initial_amount} {initial_coin}")
            await self.telegram.send_message(f"Starting with {initial_amount} {initial_coin}")

            # split the initial amount into number of balances
            for _ in range(self.config.NUMBER_OF_BALANCES):
                current_amount = (initial_amount / self.config.NUMBER_OF_BALANCES).quantize(
                    Decimal('0.00000001'), rounding=ROUND_DOWN)
                current_balance_id = await self.db.update_current_balance(initial_coin, current_amount)
                # Calculate initial coin values based on actual balance
                coin_values = await self.calculate_coin_values(initial_coin, current_amount)
                for coin, amount in coin_values.items():
                    await self.db.update_last_coin_value(current_balance_id, coin, amount)
                self.logger.info(f"Initial coin values recorded: {dict(coin_values)}")

    async def stop(self):
        self.logger.info("Stopping strategy...")
        await self.exchange.stop()

    async def handle_order_update(self, execution_report: ExecutionReport):
        """Handle order status updates"""
        if execution_report.status == ExecutionReport.STATUS_FILLED:
            self.logger.info(f"Order filled: {execution_report.symbol} order_id: {execution_report.id}, client_order_id: {execution_report.clientOrderId}")
            await self.telegram.send_message(f"Order filled: {execution_report.symbol} order_id: {execution_report.id}, client_order_id: {execution_report.clientOrderId}")

            # find current balance for the order
            current_balance = None
            all_current_balances = await self.db.get_all_current_balances()
            for cb in all_current_balances:
                if cb['order_id_1'] == execution_report.clientOrderId or cb['order_id_2'] == execution_report.clientOrderId:
                    current_balance = cb
                    break

            if current_balance is None:
                message = f"No current balance found for order {execution_report.clientOrderId}"
                self.logger.error(message)
                await self.telegram.send_message(message)
                await self.unrecoverable_error(message)
                return

            trade = await self.exchange.get_trade(execution_report.symbol, execution_report.id)
            if trade is None:
                message = f"No trade found for order {execution_report.clientOrderId}"
                await self.unrecoverable_error(message)
                return

            # check if we need to place a second order to finish the conversion
            if current_balance['order_id_1'] == execution_report.clientOrderId and current_balance['needs_bridge']:
                # place second order
                quote_amount = trade.quoteQty - trade.commission # the trade must be a sell order, so we use quoteQty
                await self.continue_conversion_order(current_balance['id'], current_balance['to_coin'], quote_amount)
                return

            # Save updated state to database
            to_amount = Decimal('0')
            if execution_report.side.lower() == 'sell':
                to_amount = trade.quoteQty
            else:
                to_amount = trade.qty
            if trade.commissionAsset == 'BNB' and current_balance['to_coin'] != 'BNB':
                await self.deduct_bnb_fee(trade.commission)
            else:
                to_amount -= trade.commission
            await self.db.update_last_coin_value(current_balance['id'], current_balance['coin'], current_balance['amount'])
            await self.db.current_balance_in_flight_completed(id=current_balance['id'], amount=to_amount)
            message = (f"Updated current coin id: {current_balance['id']}, from {current_balance['coin']} with amount {current_balance['amount']} to {current_balance['to_coin']} with amount {to_amount}")
            self.logger.info(message)
            await self.telegram.send_message(message)
        elif execution_report.status == ExecutionReport.STATUS_CANCELED or execution_report.status == ExecutionReport.STATUS_REJECTED:
            # order was canceled or rejected, update the database
            await self.db.current_balance_in_flight_canceled(id=current_balance['id'])
            message = f"Order {execution_report.id} has been canceled, order_id: {execution_report.id}, client_order_id: {execution_report.clientOrderId}"
            await self.unrecoverable_error(message)

    async def deduct_bnb_fee(self, fee: Decimal):
        balances = await self.db.get_all_current_balances()
        for balance in balances:
            if balance['coin'] == 'BNB' and not balance['in_flight'] and balance['amount'] > fee:
                await self.db.deduct_bnb_fee_from_balances(id=balance['id'], fee=fee)
                self.logger.info("Deducted BNB fee from balance {balance['id']}, fee: {fee}")
                return
        await self.unrecoverable_error(f"No BNB balance found to deduct fee {fee}")

    async def start_conversion_order(self,
                                    from_coin: str,
                                    to_coin: str,
                                    current_balance_id: int,
                                    amount: Decimal):
        self.logger.info(f"Starting conversion order for balance {current_balance_id} from {from_coin} to {to_coin} with amount {amount}")
        try:
            # Find the trading pair and whether it needs bridge
            is_buy = False
            pair = None
            needs_bridge = False
            # Check trading pairs configuration to find the pair and whether it needs bridge
            for pair_dict in self.trading_pairs:
                p = pair_dict['base']+pair_dict['quote']
                b = pair_dict['needs_bridge']
                if p == f"{to_coin}{from_coin}":
                    is_buy = True
                    pair = p
                    needs_bridge = b
                    break
                elif p == f"{from_coin}{to_coin}":
                    is_buy = False
                    pair = p
                    needs_bridge = b
                    break
            if pair is None:
                self.logger.error(f"No trading pair found for conversion of balance {current_balance_id} from {from_coin} to {to_coin}")
                return None, None

            # Place first order
            order_id_1 = None
            if needs_bridge:
                # Convert through bridge currency
                bridge = self.config.BRIDGE_CURRENCY
                self.logger.info(f"Using {bridge} as bridge currency for balance {current_balance_id} conversion of {from_coin} -> {to_coin}")

                # First order: Convert from_coin to bridge
                symbol = f"{from_coin}{bridge}"

                order_id_1 = await self.exchange.create_order(
                    symbol=symbol,
                    type='market',
                    side='sell',
                    amount=amount
                )
            else:
                # Direct conversion available
                symbol = pair
                side = 'buy' if is_buy else 'sell'
                params = {
                    'symbol': symbol,
                    'type': 'market',
                    'side': side,
                    'amount': amount
                }
                if is_buy:
                    params['quote_amount'] = amount
                    params['amount'] = None

                order_id_1 = await self.exchange.create_order(**params)

            self.logger.info(f"Placed order to convert balance {current_balance_id} from {from_coin} to {to_coin}, order_id: {order_id_1}")
            await self.telegram.send_message(f"Converting balance {current_balance_id} from {from_coin} to {to_coin}, order_id: {order_id_1}")
            await self.db.current_balance_first_order_placed(id=current_balance_id,
                                                             to_coin=to_coin,
                                                             needs_bridge=needs_bridge,
                                                             order_id_1=order_id_1,
                                                             order_symbol=symbol)
        except Exception as e:
            await self.unrecoverable_error(f"Error placing balance {current_balance_id} conversion order from {from_coin} to {to_coin}: traceback: {traceback.format_exc()}")

    async def continue_conversion_order(self, current_balance_id: int, to_coin: str, quote_amount: Decimal):
        from_coin = self.config.BRIDGE_CURRENCY
        self.logger.info(f"Continuing conversion order for balance {current_balance_id} from {from_coin} to {to_coin} with bridge amount {quote_amount}")
        try:
            second_order_id = await self.exchange.create_order(
                symbol=f"{to_coin}{from_coin}",
                type='market',
                side='buy',
                quote_amount=quote_amount
            )
            await self.db.current_balance_second_order_placed(id=current_balance_id, order_id_2=second_order_id, order_symbol=f"{to_coin}{from_coin}")
            self.logger.info(
                f"Placed second order to convert balance {current_balance_id} from {from_coin} to {to_coin}, order_id: {second_order_id}")
            await self.telegram.send_message(f"Converting balance {current_balance_id} from {from_coin} to {to_coin}")
        except Exception as e:
            await self.unrecoverable_error(f"Error continuing balance {current_balance_id} conversion order, traceback: {traceback.format_exc()}")
            return None

    async def get_pair_price(self, base: str, quote: str, needs_bridge: bool = False) -> Decimal:
        """
        Get the price of a trading pair, using bridge currency if needed

        Args:
            base: Base currency (e.g., 'ETH' in ETHBTC)
            quote: Quote currency (e.g., 'BTC' in ETHBTC)
            needs_bridge: Whether to calculate price using bridge currency

        Returns:
            Price of the trading pair
        """
        pair = f"{base}{quote}"

        try:
            if not needs_bridge:
                # Direct pair available
                ticker = await self.exchange.get_ticker(pair)
                return ticker.lastPrice
            else:
                # Need to calculate through bridge currency
                bridge = self.config.BRIDGE_CURRENCY

                # Get or fetch base/bridge price
                if base not in self.bridge_prices:
                    base_ticker = await self.exchange.get_ticker(f"{base}{bridge}")
                    self.bridge_prices[base] = base_ticker.lastPrice
                base_price = self.bridge_prices[base]

                # Get or fetch quote/bridge price
                if quote not in self.bridge_prices:
                    quote_ticker = await self.exchange.get_ticker(f"{quote}{bridge}")
                    self.bridge_prices[quote] = quote_ticker.lastPrice
                quote_price = self.bridge_prices[quote]

                # Calculate synthetic price and round down to 8 decimal places
                return (base_price / quote_price).quantize(Decimal('0.00000001'), rounding=ROUND_DOWN)

        except Exception as e:
            self.logger.error(f"Error getting price for {pair}: {e}")
            return Decimal('0')

    async def update_all_prices(self):
        """Update prices for all configured trading pairs"""
        self.bridge_prices.clear()  # Clear bridge price cache

        for pair_dict in self.trading_pairs:
            base = pair_dict['base']
            quote = pair_dict['quote']
            needs_bridge = pair_dict['needs_bridge']
            
            pair = f"{base}{quote}"
            price = await self.get_pair_price(base, quote, needs_bridge)
            if price > 0:
                self.pair_prices[pair] = price
                self.logger.debug(f"Updated price for {pair}: {price}")

    async def calculate_coin_values(self, current_coin: str, amount: Decimal) -> Dict[str, Decimal]:
        """
        Calculate equivalent balances if current amount was converted to other coins

        Args:
            amount: Amount of current coin to calculate conversions for

        Returns:
            Dictionary of potential balances in other coins
        """
        coin_values = defaultdict(Decimal)

        # Set current coin's value to actual amount
        coin_values[current_coin] = amount

        for pair_dict in self.trading_pairs:
            base = pair_dict['base']
            quote = pair_dict['quote']
            pair = f"{base}{quote}"
            price = self.pair_prices.get(pair, Decimal('0'))

            if price <= 0:
                continue

            if quote == current_coin:
                # If quote is current coin, calculate how much base we could get
                coin_values[base] = (
                    amount / price).quantize(Decimal('0.00000001'), rounding=ROUND_DOWN)
            elif base == current_coin:
                # If base is current coin, calculate how much quote we could get
                coin_values[quote] = (
                    amount * price).quantize(Decimal('0.00000001'), rounding=ROUND_DOWN)
        return coin_values

    async def calculate_variation(self, current_values: Dict[str, Decimal], current_balance_id: int) -> tuple[Decimal, str]:
        """
        Calculate maximum price variation between current and initial values

        Args:
            current_values: Current coin values

        Returns:
            Tuple of (maximum variation percentage, coin with max variation)
        """
        max_variation = Decimal('0')
        max_variation_coin = ""

        for coin, current_value in current_values.items():
            last_coin_value = await self.db.get_last_coin_value(current_balance_id, coin)
            if last_coin_value is None:
                await self.unrecoverable_error(f"No last coin value found for balance {current_balance_id} and coin {coin}")
                return Decimal('0'), ""
            initial_value = last_coin_value['last_amount']

            # Calculate variation percentage and round down to 8 decimal places
            variation = ((current_value - initial_value) /
                         initial_value).quantize(Decimal('0.00000001'), rounding=ROUND_DOWN)
            self.logger.debug(
                f"coin: {coin}, current_value: {current_value}, initial_value: {initial_value}, variation: {variation}")
            if variation > max_variation:
                max_variation = variation
                max_variation_coin = coin
        self.max_variations[current_balance_id] = f"id: {current_balance_id} -> {max_variation_coin}: {max_variation}"
        return max_variation, max_variation_coin

    async def get_deviation_factor(self, from_coin: str, to_coin: str) -> Decimal:
        """Calculate a deviation factor based on the number of balances in each coin

        Args:
            from_coin: Source coin (used in full implementation)
            to_coin: Target coin

        Returns:
            Deviation factor number
        """
        # Note: from_coin parameter is kept for API compatibility but not used in this implementation
        _ = from_coin  # Acknowledge parameter to avoid unused variable warning

        all_current_balances = await self.db.get_all_current_balances()
        all_holding_coins = self.config.TRADING_COINS + \
            [self.config.INITIAL_COIN]
        avg = int(len(all_current_balances) / len(all_holding_coins))

        to_cnt = 0
        for current_balance in all_current_balances:
            if current_balance['coin'] == to_coin or (current_balance['to_coin'] == to_coin and current_balance['in_flight']):
                to_cnt += 1

        cnt = to_cnt - avg
        if cnt <= 0:
            cnt = 1

        return self.decimal_pow(cnt, self.config.FACTOR_EXPONENT)

    async def check_pending_orders(self):
        """Check for any pending orders that might have been missed during application restart"""
        self.logger.info("Checking for pending orders...")

        # Get all in-flight balances
        in_flight_balances = await self.db.get_in_flight_balances()
        if not in_flight_balances:
            self.logger.info("No pending orders found")
            return

        self.logger.info(f"Found {len(in_flight_balances)} pending orders")

        for balance in in_flight_balances:
            order_symbol = balance['order_symbol']
            order_id = None
            if balance['order_id_1']:
                order_id = balance['order_id_1']
            elif balance['order_id_2']:
                order_id = balance['order_id_2']
                order_symbol = f"{balance['to_coin']}{self.config.BRIDGE_CURRENCY}"

            # Check first order if it exists
            if order_id:
                self.logger.info(
                    f"Checking status of order {order_id} for symbol {order_symbol}")
                execution_report = await self.exchange.get_order_status(order_symbol, order_id)

                if execution_report:
                    self.logger.info(
                        f"Found order {order_id} with status {execution_report.status}")
                    self.logger.info(
                        f"Processing missed order update for {order_id}")
                    await self.handle_order_update(execution_report)
                else:
                    self.logger.warning(
                        f"Could not retrieve status for order {order_id}")


    async def loop(self):
        """Main strategy loop"""

        # Update all prices
        await self.update_all_prices()

        # Check for any pending orders from previous runs every 20 loops
        if not hasattr(self, '_loop_count'):
            self._loop_count = 0
        self._loop_count += 1
        if self._loop_count % 20 == 0:
            await self.check_pending_orders()

        all_current_balances = await self.db.get_all_current_balances()
        for current_balance in all_current_balances:
            # Get current balance
            current_balance_id = current_balance['id']
            current_amount = current_balance['amount']
            current_coin = current_balance['coin']
            # Calculate current coin values based on actual balance
            current_coin_values = await self.calculate_coin_values(current_coin, current_amount)
            # Calculate maximum variation
            max_variation, max_variation_coin = await self.calculate_variation(current_coin_values, current_balance_id)
            if self._loop_count % 100 == 0:
                self.logger.info(f"Variations:")
                for variation in self.max_variations.values():
                    self.logger.info(f"    {variation}")
            if len(max_variation_coin) == 0:
                continue
            max_variation_factor = await self.get_deviation_factor(current_coin, max_variation_coin)

            # Log current state
            self.logger.debug(f"---Balance ID: {current_balance_id}---")
            self.logger.debug(f"Current coin: {current_coin}, Current prices: {self.pair_prices}")
            self.logger.debug(f"Current coin values: {dict(current_coin_values)}")
            self.logger.debug(f"Maximum variation: {max_variation} ({max_variation_coin})")
            self.logger.debug(
                f"Maximum variation coin balance factor: {max_variation_factor}")
            threshold = Decimal(
                '0.002') + self.config.DEVIATION_THRESHOLD * max_variation_factor
            # Check if variation exceeds threshold and place order if needed
            if max_variation > threshold and max_variation_coin != current_coin:
                # check if current_coin has orders in flight
                if current_balance['in_flight']:
                    # Get or initialize in_flight_count from memory
                    if not hasattr(self, '_in_flight_counts'):
                        self._in_flight_counts = {}

                    balance_id = current_balance['id']
                    self._in_flight_counts[balance_id] = self._in_flight_counts.get(
                        balance_id, 0) + 1

                    # Check if exceeded maximum allowed continuous in-flight loops
                    # 5 minutes with 1 second loop interval
                    if self._in_flight_counts[balance_id] > 300:
                        await self.unrecoverable_error(f"Balance {balance_id} has been in flight for too long ({self._in_flight_counts[balance_id]} loops)")
                        return

                    self.logger.info(
                        f"Current coin {current_coin} has orders in flight for {self._in_flight_counts[balance_id]} loops, skipping conversion")
                    continue
                else:
                    # Reset counter when not in flight
                    if hasattr(self, '_in_flight_counts'):
                        self._in_flight_counts.pop(current_balance['id'], None)
                self.logger.info(
                    f"Variation {max_variation} exceeds threshold {threshold} for {current_coin}->{max_variation_coin}, balance id {current_balance_id}, max_variation_factor {max_variation_factor}")
                await self.start_conversion_order(current_coin, max_variation_coin, current_balance['id'], current_amount)

    async def get_status(self) -> List[str]:
        current_balances = await self.db.get_all_current_balances()
        status = []
        status.append(f"Name: Mean Reversion Strategy")
        status.append(f"Bridge Currency: {self.config.BRIDGE_CURRENCY}")
        status.append(f"Initial Coin: {self.config.INITIAL_COIN}")
        status.append(f"Deviation Threshold: {self.config.DEVIATION_THRESHOLD}")

        status.append(f"Current Balances:")
        for balance in current_balances:
            status.append(f"---Balance ID: {balance['id']}---")
            status.append(f"    Coin: {balance['coin']}")
            status.append(f"    Amount: {balance['amount']}")
            status.append(f"    To Coin: {balance['to_coin']}")
            status.append(f"    In Flight: {balance['in_flight']}")
            status.append(f"    Order ID 1: {balance['order_id_1']}")
            status.append(f"    Order ID 2: {balance['order_id_2']}")
            status.append(
                f"    Max Variation:{self.max_variations[balance['id']]}")
        return status
