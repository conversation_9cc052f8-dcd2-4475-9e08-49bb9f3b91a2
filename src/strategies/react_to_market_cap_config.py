from dataclasses import dataclass, field
import os
from typing import Dict, List
from decimal import Decimal
import json

@dataclass
class MovingAverageCrossConfig():
    long_term: int
    short_term: int


@dataclass
class ReactToMarketCapConfig():
    @staticmethod
    def trading_pairs_from_environment() -> Dict[str, List[MovingAverageCrossConfig]]:
        json_str = os.getenv("TRADING_COINS", '{}')
        data = json.loads(json_str)
        for coin, configs in data.items():
            for config in configs:
                config['long_term'] = int(config['long_term'])
                config['short_term'] = int(config['short_term'])
        return {pair: [MovingAverageCrossConfig(config['long_term'], config['short_term']) 
                for config in configs] for pair, configs in data.items()}


    # React to market cap specific settings
    TRADING_COINS: Dict[str, List[MovingAverageCrossConfig]] = field(
        default_factory=trading_pairs_from_environment)
    INITIAL_COIN: str = "BTC"
    COINGECKO_API_URL: str = "https://api.coingecko.com/api/v3/coins/markets"
    EMA_LONG_INTERVAL: str = os.getenv("EMA_LONG_INTERVAL", "1h")
    EMA_SHORT_INTERVAL: str = os.getenv("EMA_SHORT_INTERVAL", "5m")
    DEVIATION_THRESHOLD: Decimal = Decimal(
        os.getenv("DEVIATION_THRESHOLD", "0.001"))


