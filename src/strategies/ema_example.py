from decimal import Decimal
from .base_strategy import BaseStrategy
from utils.technical_indicators import TechnicalIndicators
from exchange.execution_report import ExecutionReport
from utils.telegram_client import TelegramClient
from exchange.base_exchange import BaseExchange
from db.trading import TradingDB

class EMAStrategy(BaseStrategy):
    def __init__(self, db: TradingDB, telegram: TelegramClient, exchange: BaseExchange = None):
        super().__init__(exchange, telegram)
        self.db = db
        self.symbol = "BTCUSDT"
        self.short_period = 12
        self.long_period = 26
        
    async def calculate_emas(self):
        """Calculate current EMA values"""
        # Get enough historical prices for the longer EMA period
        prices = await self.exchange.get_historical_prices(self.symbol, self.long_period * 3)
        
        if not prices:
            await self.unrecoverable_error(f"Could not get prices for {self.symbol}")
            return None, None
            
        # Calculate EMAs
        short_ema = TechnicalIndicators.get_current_ema(prices, self.short_period)
        long_ema = TechnicalIndicators.get_current_ema(prices, self.long_period)
        
        return short_ema, long_ema
        
    async def loop(self):
        """Main strategy loop"""
        short_ema, long_ema = await self.calculate_emas()
        
        if short_ema is None or long_ema is None:
            return
            
        current_price = await self.exchange.get_current_price(self.symbol)
        
        self.logger.info(f"Current price: {current_price}")
        self.logger.info(f"Short EMA ({self.short_period}): {short_ema}")
        self.logger.info(f"Long EMA ({self.long_period}): {long_ema}")
        
        # Example trading logic (EMA crossover)
        if short_ema > long_ema:
            self.logger.info("Bullish signal: Short EMA above Long EMA")
            # Implement buy logic here
        elif short_ema < long_ema:
            self.logger.info("Bearish signal: Short EMA below Long EMA")
            # Implement sell logic here
            
    async def handle_order_update(self, execution_report: ExecutionReport):
        """Handle order updates"""
        if execution_report.status == ExecutionReport.STATUS_FILLED:
            self.logger.info(f"Order filled: {execution_report.symbol}")
            await self.telegram.send_message(
                f"Order filled: {execution_report.symbol} {execution_report.side} "
                f"Amount: {execution_report.executedQty} Price: {execution_report.price}"
            )