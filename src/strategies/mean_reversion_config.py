from dataclasses import dataclass, field
import os
from typing import List
from decimal import Decimal

@dataclass
class MeanReversionConfig():
    # Mean reversion specific settings
    TRADING_COINS: List[str] = field(default_factory=lambda: [
        coin.strip() for coin in os.getenv("TRADING_COINS", "ETH,BTC").split(",")
    ])
    DEVIATION_THRESHOLD: Decimal = Decimal(
        os.getenv("DEVIATION_THRESHOLD", "0.005"))
    NUMBER_OF_BALANCES: int = int(os.getenv("NUMBER_OF_BALANCES", "20"))
    BRIDGE_CURRENCY: str = os.getenv("BRIDGE_CURRENCY", "USDT")
    INITIAL_COIN: str = os.getenv("INITIAL_COIN", "ETH")
    FACTOR_EXPONENT: Decimal = Decimal(os.getenv("FACTOR_EXPONENT", "1.65"))
