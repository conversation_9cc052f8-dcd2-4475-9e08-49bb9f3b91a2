from abc import ABC, abstractmethod
import asyncio
from decimal import Decimal
from typing import Dict, Any
from exchange.base_exchange import BaseExchange
from exchange.execution_report import ExecutionReport
from utils.logging import Logger
from utils.telegram_client import TelegramClient
import traceback

class BaseStrategy(ABC):
    def __init__(self, exchange: BaseExchange, telegram: TelegramClient):
        self.logger = Logger.get_logger()
        self.exchange = exchange
        self.telegram = telegram
        self.lock = asyncio.Lock()
        self.is_running = False
        exchange.set_order_update_callback(self.handle_order_update_with_lock)

    @abstractmethod
    async def loop(self):
        pass
    
    async def loop_with_lock(self):
        if not self.is_running:
            self.logger.info("Strategy not running, skipping loop")
            await asyncio.sleep(1)
            return
        
        async with self.lock:
            try:
                await self.loop()
            except Exception as e:
                await self.unrecoverable_error(f"Error in strategy loop: traceback: {traceback.format_exc()}")
    
    @abstractmethod
    async def handle_order_update(self, execution_report: ExecutionReport):
        pass
    
    async def handle_order_update_with_lock(self, execution_report: ExecutionReport):
        async with self.lock:
            await self.handle_order_update(execution_report)
    
    @abstractmethod
    def get_status(self) -> Dict[str, Any]:
        """
        Get strategy status information
        
        Returns:
            Dictionary containing strategy status information
        """
        pass
    
    @abstractmethod
    async def start(self):
        pass
    
    async def baseStart(self):
        await self.start()
        self.is_running = True
        self.logger.info("Strategy started")
    
    @abstractmethod
    async def stop(self):
        pass
        
    async def baseStop(self):
        await self.stop()
        self.is_running = False
        self.logger.info("Strategy stopped")
    
    async def unrecoverable_error(self, msg:str):
        self.logger.error(f"Unrecoverable error: {msg}")
        await self.telegram.send_message(f"Unrecoverable error: {msg}")
        await self.baseStop()

    def decimal_ln(self, x):
        from decimal import Decimal
        import math
        return Decimal(math.log(float(x)))

    def decimal_exp(self, x):
        from decimal import Decimal
        import math
        return Decimal(math.exp(float(x)))

    # 主计算函数
    def decimal_pow(self, n: Decimal, exponent: Decimal) -> Decimal:
        n = Decimal(n)
        exponent = Decimal(exponent)
        return self.decimal_exp(exponent * self.decimal_ln(n))

    # 示例计算 n^1.2
    # n = Decimal('4')
    # exponent = Decimal('1.2')
    # result = decimal_pow(n, exponent)
    # print(f"{n}^{exponent} = {result}")
