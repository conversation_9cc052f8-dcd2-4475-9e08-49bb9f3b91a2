from typing import Type
from .base_strategy import BaseStrategy
from .mean_reversion import MeanReversionStrategy
from .react_to_market_cap import ReactToMarketCapStrategy
from utils.logging import Logger
from db.trading import TradingDB
from utils.telegram_client import TelegramClient
from exchange.base_exchange import BaseExchange

class StrategyFactory:
    _strategies = {
        "mean_reversion": MeanReversionStrategy,
        "react_to_market_cap": ReactToMarketCapStrategy,
    }
    
    @classmethod
    def get_strategy_class(cls, strategy_name: str) -> Type[BaseStrategy]:
        strategy_class = cls._strategies.get(strategy_name.lower())
        if not strategy_class:
            raise ValueError(f"Strategy '{strategy_name}' not found. Available strategies: {list(cls._strategies.keys())}")
        return strategy_class
    
    @classmethod
    def create_strategy(cls, 
                       strategy_name: str, 
                       db: TradingDB, 
                       telegram: TelegramClient, 
                       exchange: BaseExchange) -> BaseStrategy:
        logger = Logger.get_logger()
        strategy_class = cls.get_strategy_class(strategy_name)
        logger.info(f"Creating strategy: {strategy_name}")
        return strategy_class(db, telegram, exchange)