# Trading Mode (live or backtest)
TRADING_MODE=live

# Exchange API Keys
EXCHANGE_API_KEY=your_api_key
EXCHANGE_SECRET_KEY=your_secret_key

# Database (PostgreSQL)
DB_HOST=localhost
DB_PORT=5432
DB_NAME=trading
DB_USER=trading
DB_PASSWORD=123456

# Telegram Notifications
TELEGRAM_BOT_TOKEN=your_bot_token
TELEGRAM_CHAT_ID=your_chat_id

# React to Market Cap Strategy Configuration
INITIAL_COIN=BTC
EMA_LONG_INTERVAL=1h
EMA_SHORT_INTERVAL=5m
DEVIATION_THRESHOLD=0.001

# Trading Coins Configuration (JSON format)
TRADING_COINS={"ETH":[{"long_term":26,"short_term":12}],"BNB":[{"long_term":24,"short_term":8}]}

# Backtest Configuration (only used when TRADING_MODE=backtest)
BACKTEST_START_TIME=2023-01-01T00:00:00Z
BACKTEST_END_TIME=2023-01-31T23:59:59Z
