version: '3.8'

services:
  db1:
    image: postgres:13
    environment:
      POSTGRES_DB: trading
      POSTGRES_USER: trading
      POSTGRES_PASSWORD: 123456
    volumes:
      - ./src/db/trading.sql:/docker-entrypoint-initdb.d/trading.sql
      - pgdata-backtest1:/var/lib/postgresql/data
    ports:
      - "15431:5432"
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U trading -d trading"]
      interval: 5s
      timeout: 5s
      retries: 5
      start_period: 15s


  db2:
    image: postgres:13
    environment:
      POSTGRES_DB: trading
      POSTGRES_USER: trading
      POSTGRES_PASSWORD: 123456
    volumes:
      - ./src/db/trading.sql:/docker-entrypoint-initdb.d/trading.sql
      - pgdata-backtest2:/var/lib/postgresql/data
    ports:
      - "15432:5432"
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U trading -d trading"]
      interval: 5s
      timeout: 5s
      retries: 5
      start_period: 15s

  db3:
    image: postgres:13
    environment:
      POSTGRES_DB: trading
      POSTGRES_USER: trading
      POSTGRES_PASSWORD: 123456
    volumes:
      - ./src/db/trading.sql:/docker-entrypoint-initdb.d/trading.sql
      - pgdata-backtest3:/var/lib/postgresql/data
    ports:
      - "15433:5432"
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U trading -d trading"]
      interval: 5s
      timeout: 5s
      retries: 5
      start_period: 15s

  db4:
    image: postgres:13
    environment:
      POSTGRES_DB: trading
      POSTGRES_USER: trading
      POSTGRES_PASSWORD: 123456
    volumes:
      - ./src/db/trading.sql:/docker-entrypoint-initdb.d/trading.sql
      - pgdata-backtest4:/var/lib/postgresql/data
    ports:
      - "15434:5432"
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U trading -d trading"]
      interval: 5s
      timeout: 5s
      retries: 5
      start_period: 15s

  db5:
    image: postgres:13
    environment:
      POSTGRES_DB: trading
      POSTGRES_USER: trading
      POSTGRES_PASSWORD: 123456
    volumes:
      - ./src/db/trading.sql:/docker-entrypoint-initdb.d/trading.sql
      - pgdata-backtest5:/var/lib/postgresql/data
    ports:
      - "15435:5432"
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U trading -d trading"]
      interval: 5s
      timeout: 5s
      retries: 5
      start_period: 15s

  db6:
    image: postgres:13
    environment:
      POSTGRES_DB: trading
      POSTGRES_USER: trading
      POSTGRES_PASSWORD: 123456
    volumes:
      - ./src/db/trading.sql:/docker-entrypoint-initdb.d/trading.sql
      - pgdata-backtest6:/var/lib/postgresql/data
    ports:
      - "15436:5432"
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U trading -d trading"]
      interval: 5s
      timeout: 5s
      retries: 5
      start_period: 15s

  db7:
    image: postgres:13
    environment:
      POSTGRES_DB: trading
      POSTGRES_USER: trading
      POSTGRES_PASSWORD: 123456
    volumes:
      - ./src/db/trading.sql:/docker-entrypoint-initdb.d/trading.sql
      - pgdata-backtest7:/var/lib/postgresql/data
    ports:
      - "15437:5432"
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U trading -d trading"]
      interval: 5s
      timeout: 5s
      retries: 5
      start_period: 15s

  trading-bot1:
    build: .
    environment:
      - DB_HOST=db1
      - DB_PORT=5432
      - DB_NAME=trading
      - DB_USER=trading
      - DB_PASSWORD=123456
      - INITIAL_COIN=BTC
      - EXCHANGE_API_KEY=${EXCHANGE_API_KEY}
      - EXCHANGE_SECRET_KEY=${EXCHANGE_SECRET_KEY}
      - TELEGRAM_BOT_TOKEN=${TELEGRAM_TOKEN}
      - TELEGRAM_CHAT_ID=${TELEGRAM_CHAT_ID}
      - TRADING_MODE=backtest
      - BACKTEST_START_TIME=2025-01-15T00:00:00Z
      - BACKTEST_END_TIME=2025-04-15T23:59:59Z
      - TRADING_COINS={"ETH":[{"long_term":24,"short_term":1}],"SOL":[{"long_term":24,"short_term":1}],"XRP":[{"long_term":24,"short_term":1}],"DOGE":[{"long_term":24,"short_term":1}],"TRX":[{"long_term":24,"short_term":1}],"ADA":[{"long_term":24,"short_term":1}],"LINK":[{"long_term":24,"short_term":1}],"AVAX":[{"long_term":24,"short_term":1}],"XLM":[{"long_term":24,"short_term":1}],"TON":[{"long_term":24,"short_term":1}],"SUI":[{"long_term":24,"short_term":1}],"HBAR":[{"long_term":24,"short_term":1}],"BCH":[{"long_term":24,"short_term":1}],"DOT":[{"long_term":24,"short_term":1}],"LTC":[{"long_term":24,"short_term":1}]}
      - EMA_SHORT_INTERVAL=30m
      - EMA_LONG_INTERVAL=30m
      - DEVIATION_THRESHOLD=100
      - LOG_DIR=/app/logs
      - LOG_LEVEL=info
      - LOG_MAX_SIZE=100
      - LOG_MAX_BACKUPS=5
      - LOG_MAX_AGE=30
      - LOG_COMPRESS=true
    volumes:
      - ./docker-logs-1:/app/logs
      - ./data1:/app/data
    depends_on:
      db1:
        condition: service_healthy
    command: [sh, -c, "sleep 3600"]


  trading-bot2:
    build: .
    environment:
      - DB_HOST=db2
      - DB_PORT=5432
      - DB_NAME=trading
      - DB_USER=trading
      - DB_PASSWORD=123456
      - INITIAL_COIN=BTC
      - EXCHANGE_API_KEY=${EXCHANGE_API_KEY}
      - EXCHANGE_SECRET_KEY=${EXCHANGE_SECRET_KEY}
      - TELEGRAM_BOT_TOKEN=${TELEGRAM_TOKEN}
      - TELEGRAM_CHAT_ID=${TELEGRAM_CHAT_ID}
      - TRADING_MODE=backtest
      - BACKTEST_START_TIME=2025-03-10T00:00:00Z
      - BACKTEST_END_TIME=2025-05-10T23:59:59Z
      - TRADING_COINS={"ETH":[{"long_term":24,"short_term":1}],"SOL":[{"long_term":24,"short_term":1}],"XRP":[{"long_term":24,"short_term":1}],"DOGE":[{"long_term":24,"short_term":1}],"TRX":[{"long_term":24,"short_term":1}],"ADA":[{"long_term":24,"short_term":1}],"LINK":[{"long_term":24,"short_term":1}],"AVAX":[{"long_term":24,"short_term":1}],"XLM":[{"long_term":24,"short_term":1}],"TON":[{"long_term":24,"short_term":1}],"SUI":[{"long_term":24,"short_term":1}],"HBAR":[{"long_term":24,"short_term":1}],"BCH":[{"long_term":24,"short_term":1}],"DOT":[{"long_term":24,"short_term":1}],"LTC":[{"long_term":24,"short_term":1}]}
      - EMA_SHORT_INTERVAL=30m
      - EMA_LONG_INTERVAL=30m
      - DEVIATION_THRESHOLD=0
      # 65.351717944600523333279158045636

    volumes:
      - ./docker-logs-2:/app/logs
      - ./data2:/app/data
    depends_on:
      db2:
        condition: service_healthy

  trading-bot3:
    build: .
    environment:
      - DB_HOST=db3
      - DB_PORT=5432
      - DB_NAME=trading
      - DB_USER=trading
      - DB_PASSWORD=123456
      - INITIAL_COIN=BTC
      - EXCHANGE_API_KEY=${EXCHANGE_API_KEY}
      - EXCHANGE_SECRET_KEY=${EXCHANGE_SECRET_KEY}
      - TELEGRAM_BOT_TOKEN=${TELEGRAM_TOKEN}
      - TELEGRAM_CHAT_ID=${TELEGRAM_CHAT_ID}
      - TRADING_MODE=backtest
      - BACKTEST_START_TIME=2025-03-10T00:00:00Z
      - BACKTEST_END_TIME=2025-05-10T23:59:59Z
      - TRADING_COINS={"ETH":[{"long_term":24,"short_term":1}],"SOL":[{"long_term":24,"short_term":1}],"XRP":[{"long_term":24,"short_term":1}],"DOGE":[{"long_term":24,"short_term":1}],"TRX":[{"long_term":24,"short_term":1}],"ADA":[{"long_term":24,"short_term":1}],"LINK":[{"long_term":24,"short_term":1}],"AVAX":[{"long_term":24,"short_term":1}],"XLM":[{"long_term":24,"short_term":1}],"TON":[{"long_term":24,"short_term":1}],"SUI":[{"long_term":24,"short_term":1}],"HBAR":[{"long_term":24,"short_term":1}],"BCH":[{"long_term":24,"short_term":1}],"DOT":[{"long_term":24,"short_term":1}],"LTC":[{"long_term":24,"short_term":1}]}
      - EMA_SHORT_INTERVAL=1h
      - EMA_LONG_INTERVAL=1h
      - DEVIATION_THRESHOLD=0
      # 39.039651496995497205435071901131%

    volumes:
      - ./docker-logs-3:/app/logs
      - ./data3:/app/data
    depends_on:
      db3:
        condition: service_healthy
    command: [sh, -c, "sleep 3600"]


  trading-bot4:
    build: .
    environment:
      - DB_HOST=db4
      - DB_PORT=5432
      - DB_NAME=trading
      - DB_USER=trading
      - DB_PASSWORD=123456
      - INITIAL_COIN=BTC
      - EXCHANGE_API_KEY=${EXCHANGE_API_KEY}
      - EXCHANGE_SECRET_KEY=${EXCHANGE_SECRET_KEY}
      - TELEGRAM_BOT_TOKEN=${TELEGRAM_TOKEN}
      - TELEGRAM_CHAT_ID=${TELEGRAM_CHAT_ID}
      - TRADING_MODE=backtest
      - BACKTEST_START_TIME=2025-03-10T00:00:00Z
      - BACKTEST_END_TIME=2025-05-10T23:59:59Z
      - TRADING_COINS={"ETH":[{"long_term":24,"short_term":2}],"SOL":[{"long_term":24,"short_term":2}],"XRP":[{"long_term":24,"short_term":2}],"DOGE":[{"long_term":24,"short_term":2}],"TRX":[{"long_term":24,"short_term":2}],"ADA":[{"long_term":24,"short_term":2}],"LINK":[{"long_term":24,"short_term":2}],"AVAX":[{"long_term":24,"short_term":2}],"XLM":[{"long_term":24,"short_term":2}],"TON":[{"long_term":24,"short_term":2}],"SUI":[{"long_term":24,"short_term":2}],"HBAR":[{"long_term":24,"short_term":2}],"BCH":[{"long_term":24,"short_term":2}],"DOT":[{"long_term":24,"short_term":2}],"LTC":[{"long_term":24,"short_term":2}]}
      - EMA_SHORT_INTERVAL=30m
      - EMA_LONG_INTERVAL=30m
      - DEVIATION_THRESHOLD=0
      # 101.61943220804676935333650586579

    volumes:
      - ./docker-logs-4:/app/logs
      - ./data4:/app/data
    depends_on:
      db4:
        condition: service_healthy
    command: [sh, -c, "sleep 3600"]

  trading-bot5:
    build: .
    environment:
      - DB_HOST=db5
      - DB_PORT=5432
      - DB_NAME=trading
      - DB_USER=trading
      - DB_PASSWORD=123456
      - INITIAL_COIN=BTC
      - EXCHANGE_API_KEY=${EXCHANGE_API_KEY}
      - EXCHANGE_SECRET_KEY=${EXCHANGE_SECRET_KEY}
      - TELEGRAM_BOT_TOKEN=${TELEGRAM_TOKEN}
      - TELEGRAM_CHAT_ID=${TELEGRAM_CHAT_ID}
      - TRADING_MODE=backtest
      - BACKTEST_START_TIME=2025-01-15T00:00:00Z
      - BACKTEST_END_TIME=2025-04-15T23:59:59Z
      - TRADING_COINS={"ETH":[{"long_term":24,"short_term":8}],"SOL":[{"long_term":24,"short_term":8}],"XRP":[{"long_term":24,"short_term":8}],"DOGE":[{"long_term":24,"short_term":8}],"TRX":[{"long_term":24,"short_term":8}],"ADA":[{"long_term":24,"short_term":8}],"LINK":[{"long_term":24,"short_term":8}],"AVAX":[{"long_term":24,"short_term":8}],"XLM":[{"long_term":24,"short_term":8}],"TON":[{"long_term":24,"short_term":8}],"SUI":[{"long_term":24,"short_term":8}],"HBAR":[{"long_term":24,"short_term":8}],"BCH":[{"long_term":24,"short_term":8}],"DOT":[{"long_term":24,"short_term":8}],"LTC":[{"long_term":24,"short_term":8}]}
      - EMA_SHORT_INTERVAL=15m
      - EMA_LONG_INTERVAL=15m
      - DEVIATION_THRESHOLD=0

    volumes:
      - ./docker-logs-5:/app/logs
      - ./data5:/app/data
    depends_on:
      db5:
        condition: service_healthy
    command: [sh, -c, "sleep 3600"]

  trading-bot6:
    build: .
    environment:
      - DB_HOST=db6
      - DB_PORT=5432
      - DB_NAME=trading
      - DB_USER=trading
      - DB_PASSWORD=123456
      - INITIAL_COIN=BTC
      - EXCHANGE_API_KEY=${EXCHANGE_API_KEY}
      - EXCHANGE_SECRET_KEY=${EXCHANGE_SECRET_KEY}
      - TELEGRAM_BOT_TOKEN=${TELEGRAM_TOKEN}
      - TELEGRAM_CHAT_ID=${TELEGRAM_CHAT_ID}
      - TRADING_MODE=backtest
      - BACKTEST_START_TIME=2025-01-15T00:00:00Z
      - BACKTEST_END_TIME=2025-04-15T23:59:59Z
      - TRADING_COINS={"ETH":[{"long_term":24,"short_term":8}],"SOL":[{"long_term":24,"short_term":8}],"XRP":[{"long_term":24,"short_term":8}],"DOGE":[{"long_term":24,"short_term":8}],"TRX":[{"long_term":24,"short_term":8}],"ADA":[{"long_term":24,"short_term":8}],"LINK":[{"long_term":24,"short_term":8}],"AVAX":[{"long_term":24,"short_term":8}],"XLM":[{"long_term":24,"short_term":8}],"TON":[{"long_term":24,"short_term":8}],"SUI":[{"long_term":24,"short_term":8}],"HBAR":[{"long_term":24,"short_term":8}],"BCH":[{"long_term":24,"short_term":8}],"DOT":[{"long_term":24,"short_term":8}],"LTC":[{"long_term":24,"short_term":8}]}
      - EMA_SHORT_INTERVAL=15m
      - EMA_LONG_INTERVAL=15m
      - DEVIATION_THRESHOLD=0.002

    volumes:
      - ./docker-logs-6:/app/logs
      - ./data6:/app/data
    depends_on:
      db6:
        condition: service_healthy
    command: [sh, -c, "sleep 3600"]

  trading-bot7:
    build: .
    environment:
      - DB_HOST=db7
      - DB_PORT=5432
      - DB_NAME=trading
      - DB_USER=trading
      - DB_PASSWORD=123456
      - INITIAL_COIN=BTC
      - EXCHANGE_API_KEY=${EXCHANGE_API_KEY}
      - EXCHANGE_SECRET_KEY=${EXCHANGE_SECRET_KEY}
      - TELEGRAM_BOT_TOKEN=${TELEGRAM_TOKEN}
      - TELEGRAM_CHAT_ID=${TELEGRAM_CHAT_ID}
      - TRADING_MODE=backtest
      - BACKTEST_START_TIME=2025-01-15T00:00:00Z
      - BACKTEST_END_TIME=2025-04-15T23:59:59Z
      - TRADING_COINS={"ETH":[{"long_term":24,"short_term":5}],"SOL":[{"long_term":24,"short_term":5}],"XRP":[{"long_term":24,"short_term":5}],"DOGE":[{"long_term":24,"short_term":5}],"TRX":[{"long_term":24,"short_term":5}],"ADA":[{"long_term":24,"short_term":5}],"LINK":[{"long_term":24,"short_term":5}],"AVAX":[{"long_term":24,"short_term":5}],"XLM":[{"long_term":24,"short_term":5}],"TON":[{"long_term":24,"short_term":5}],"SUI":[{"long_term":24,"short_term":5}],"HBAR":[{"long_term":24,"short_term":5}],"BCH":[{"long_term":24,"short_term":5}],"DOT":[{"long_term":24,"short_term":5}],"LTC":[{"long_term":24,"short_term":5}]}
      - EMA_SHORT_INTERVAL=1h
      - EMA_LONG_INTERVAL=1h
      - DEVIATION_THRESHOLD=0.002

    volumes:
      - ./docker-logs-7:/app/logs
      - ./data7:/app/data
    depends_on:
      db7:
        condition: service_healthy
    command: [sh, -c, "sleep 3600"]


volumes:
  pgdata-backtest1:
  pgdata-backtest2:
  pgdata-backtest3:
  pgdata-backtest4:
  pgdata-backtest5:
  pgdata-backtest6:
  pgdata-backtest7:
