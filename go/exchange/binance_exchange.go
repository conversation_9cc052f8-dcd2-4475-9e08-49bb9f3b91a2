package exchange

import (
	"context"
	"fmt"
	"log"
	"os"
	"strings"
	"sync"
	"time"

	"github.com/adshao/go-binance/v2"
	"github.com/kenny/trading-bot/go/db"
	"github.com/shopspring/decimal"
)

// BinanceExchange implements the Exchange interface for Binance
type BinanceExchange struct {
	apiKey             string
	secretKey          string
	client             *binance.Client
	logger             *log.Logger
	symbolInfo         map[string]SymbolInfo
	orderUpdateCallback func(*ExecutionReport)
	klinesCache        map[string]map[int64][]interface{}
	pricesCache        map[string]struct {
		prices    []decimal.Decimal
		timestamp time.Time
	}
	tradingDB          *db.TradingDB // Database connection for klines data
	mu                 sync.RWMutex
}

// NewBinanceExchange creates a new BinanceExchange instance
func NewBinanceExchange(apiKey, secretKey string, tradingDB *db.TradingDB) *BinanceExchange {
	logger := log.New(os.Stdout, "BINANCE: ", log.LstdFlags)

	return &BinanceExchange{
		apiKey:      apiKey,
		secretKey:   secretKey,
		logger:      logger,
		symbolInfo:  make(map[string]SymbolInfo),
		klinesCache: make(map[string]map[int64][]interface{}),
		pricesCache: make(map[string]struct {
			prices    []decimal.Decimal
			timestamp time.Time
		}),
		tradingDB: tradingDB,
	}
}

// Start initializes the Binance exchange
func (b *BinanceExchange) Start(ctx context.Context) error {
	b.logger.Println("Starting BinanceExchange...")

	// Initialize Binance client
	b.client = binance.NewClient(b.apiKey, b.secretKey)

	// Set up user data stream for order updates
	// This would be implemented in a production version

	return nil
}

// Stop cleans up resources
func (b *BinanceExchange) Stop(ctx context.Context) error {
	b.logger.Println("Stopping BinanceExchange...")

	// Close user data stream
	// This would be implemented in a production version

	return nil
}

// GetTicker returns ticker information for a symbol
func (b *BinanceExchange) GetTicker(ctx context.Context, symbol string) (*Ticker, error) {
	// Get price
	price, err := b.client.NewListPricesService().Symbol(symbol).Do(ctx)
	if err != nil {
		return nil, fmt.Errorf("failed to get price: %w", err)
	}

	if len(price) == 0 {
		return nil, fmt.Errorf("no price found for symbol: %s", symbol)
	}

	// Parse time values
	now := time.Now()
	openTime := now.Add(-24 * time.Hour)
	closeTime := now

	// Convert string values to decimal
	lastPrice, _ := decimal.NewFromString(price[0].Price)

	// Get 24hr stats
	stats, err := b.client.NewListPriceChangeStatsService().Symbol(symbol).Do(ctx)
	if err != nil {
		return nil, fmt.Errorf("failed to get ticker stats: %w", err)
	}

	if len(stats) == 0 {
		return nil, fmt.Errorf("no stats found for symbol: %s", symbol)
	}

	stat := stats[0]

	priceChange, _ := decimal.NewFromString(stat.PriceChange)
	priceChangePercent, _ := decimal.NewFromString(stat.PriceChangePercent)
	weightedAvgPrice, _ := decimal.NewFromString(stat.WeightedAvgPrice)
	prevClosePrice, _ := decimal.NewFromString(stat.PrevClosePrice)
	bidPrice, _ := decimal.NewFromString("0") // Not available in this API
	askPrice, _ := decimal.NewFromString("0") // Not available in this API
	openPrice, _ := decimal.NewFromString(stat.OpenPrice)
	highPrice, _ := decimal.NewFromString(stat.HighPrice)
	lowPrice, _ := decimal.NewFromString(stat.LowPrice)
	volume, _ := decimal.NewFromString(stat.Volume)
	quoteVolume, _ := decimal.NewFromString(stat.QuoteVolume)

	return &Ticker{
		Symbol:            symbol,
		PriceChange:       priceChange,
		PriceChangePercent: priceChangePercent,
		WeightedAvgPrice:  weightedAvgPrice,
		PrevClosePrice:    prevClosePrice,
		LastPrice:         lastPrice,
		BidPrice:          bidPrice,
		AskPrice:          askPrice,
		OpenPrice:         openPrice,
		HighPrice:         highPrice,
		LowPrice:          lowPrice,
		Volume:            volume,
		QuoteVolume:       quoteVolume,
		OpenTime:          openTime,
		CloseTime:         closeTime,
	}, nil
}

// GetExchangeInfo returns exchange information
func (b *BinanceExchange) GetExchangeInfo(ctx context.Context) (map[string]SymbolInfo, error) {
	b.mu.RLock()
	if len(b.symbolInfo) > 0 {
		result := make(map[string]SymbolInfo, len(b.symbolInfo))
		for k, v := range b.symbolInfo {
			result[k] = v
		}
		b.mu.RUnlock()
		return result, nil
	}
	b.mu.RUnlock()

	info, err := b.client.NewExchangeInfoService().Do(ctx)
	if err != nil {
		return nil, fmt.Errorf("failed to get exchange info: %w", err)
	}

	b.mu.Lock()
	defer b.mu.Unlock()

	b.symbolInfo = make(map[string]SymbolInfo, len(info.Symbols))

	for _, symbol := range info.Symbols {
		if symbol.Status != "TRADING" {
			continue
		}

		// Extract filters
		var minPrice, maxPrice, tickSize decimal.Decimal
		var minQty, maxQty, stepSize decimal.Decimal

		for _, filter := range symbol.Filters {
			filterType := filter["filterType"].(string)

			if filterType == "PRICE_FILTER" {
				minPrice, _ = decimal.NewFromString(filter["minPrice"].(string))
				maxPrice, _ = decimal.NewFromString(filter["maxPrice"].(string))
				tickSize, _ = decimal.NewFromString(filter["tickSize"].(string))
			} else if filterType == "LOT_SIZE" {
				minQty, _ = decimal.NewFromString(filter["minQty"].(string))
				maxQty, _ = decimal.NewFromString(filter["maxQty"].(string))
				stepSize, _ = decimal.NewFromString(filter["stepSize"].(string))
			}
		}

		b.symbolInfo[symbol.Symbol] = SymbolInfo{
			BaseAsset:           symbol.BaseAsset,
			QuoteAsset:          symbol.QuoteAsset,
			BaseAssetPrecision:  symbol.BaseAssetPrecision,
			QuoteAssetPrecision: symbol.QuoteAssetPrecision,
			Status:              symbol.Status,
			MinPrice:            minPrice,
			MaxPrice:            maxPrice,
			TickSize:            tickSize,
			MinQty:              minQty,
			MaxQty:              maxQty,
			StepSize:            stepSize,
		}
	}

	result := make(map[string]SymbolInfo, len(b.symbolInfo))
	for k, v := range b.symbolInfo {
		result[k] = v
	}

	return result, nil
}

// GetHistoricalPrices returns historical closing prices for a symbol
func (b *BinanceExchange) GetHistoricalPrices(ctx context.Context, symbol string, lookbackPeriods int, interval string) ([]decimal.Decimal, error) {
	// Get historical data
	dataPoints, err := b.GetKlines(ctx, symbol, interval, lookbackPeriods)
	if err != nil {
		return nil, err
	}

    // Extract closing prices
    prices := make([]decimal.Decimal, 0, lookbackPeriods)
    for i := len(dataPoints) - lookbackPeriods; i < len(dataPoints); i++ {
        if i >= 0 {
            prices = append(prices, dataPoints[i].Close)
        }
    }

    // Pad with zeros if we don't have enough data
    for len(prices) < lookbackPeriods {
        prices = append([]decimal.Decimal{decimal.Zero}, prices...)
    }

    return prices, nil
}

// GetCurrentPrice returns the current price for a symbol
func (b *BinanceExchange) GetCurrentPrice(ctx context.Context, symbol string) (decimal.Decimal, error) {
	ticker, err := b.GetTicker(ctx, symbol)
	if err != nil {
		return decimal.Zero, err
	}
	return ticker.LastPrice, nil
}

// GetBalance returns the account balance
func (b *BinanceExchange) GetBalance(ctx context.Context, currency string) (*Balance, error) {
	account, err := b.client.NewGetAccountService().Do(ctx)
	if err != nil {
		return nil, fmt.Errorf("failed to get account: %w", err)
	}

	balances := make(map[string]AssetBalance)

	for _, balance := range account.Balances {
		free, _ := decimal.NewFromString(balance.Free)
		locked, _ := decimal.NewFromString(balance.Locked)

		if free.IsZero() && locked.IsZero() {
			continue
		}

		balances[balance.Asset] = AssetBalance{
			Asset:  balance.Asset,
			Free:   free,
			Locked: locked,
		}
	}

	result := &Balance{
		Balances:    balances,
		UpdateTime:  time.Unix(int64(account.UpdateTime/1000), 0),
		AccountType: account.AccountType,
		Permissions: account.Permissions,
	}

	if currency != "" {
		// Filter for specific currency
		if asset, exists := balances[currency]; exists {
			filteredBalances := map[string]AssetBalance{
				currency: asset,
			}
			result.Balances = filteredBalances
		} else {
			result.Balances = make(map[string]AssetBalance)
		}
	}

	return result, nil
}

// GetBalanceTotal returns the total balance in USDT
func (b *BinanceExchange) GetBalanceTotal(ctx context.Context) (decimal.Decimal, error) {
	balance, err := b.GetBalance(ctx, "")
	if err != nil {
		return decimal.Zero, err
	}

	total := decimal.Zero

	for asset, assetBalance := range balance.Balances {
		if asset == "USDT" {
			total = total.Add(assetBalance.Free.Add(assetBalance.Locked))
			continue
		}

		// Get price in USDT
		symbol := asset + "USDT"
		price, err := b.GetCurrentPrice(ctx, symbol)
		if err != nil {
			// Skip assets that don't have a USDT pair
			continue
		}

		assetValue := assetBalance.Free.Add(assetBalance.Locked).Mul(price)
		total = total.Add(assetValue)
	}

	return total, nil
}

// CreateOrder creates a new order
func (b *BinanceExchange) CreateOrder(ctx context.Context, symbol, orderType, side string, amount, quoteAmount, price decimal.Decimal) (string, error) {
	// Get lot size to round amount
	min, max, stepSize, err := b.GetLotSize(ctx, symbol)
	if err != nil {
		return "", fmt.Errorf("failed to get lot size: %w", err)
	}

	// Round amount to step size
	if !amount.IsZero() {
		amount = b.roundStepSize(amount, stepSize)

		// Check min/max constraints
		if amount.LessThan(min) {
			return "", fmt.Errorf("amount %s is less than minimum %s", amount, min)
		}
		if amount.GreaterThan(max) {
			return "", fmt.Errorf("amount %s is greater than maximum %s", amount, max)
		}
	}

	// Create order service
	var orderService *binance.CreateOrderService

	// Set common parameters
	orderService = b.client.NewCreateOrderService().
		Symbol(symbol).
		Side(binance.SideType(strings.ToUpper(side)))

	// Set order type specific parameters
	switch strings.ToUpper(orderType) {
	case "LIMIT":
		orderService = orderService.
			Type(binance.OrderTypeLimit).
			TimeInForce(binance.TimeInForceTypeGTC).
			Price(price.String()).
			Quantity(amount.String())
	case "MARKET":
		orderService = orderService.Type(binance.OrderTypeMarket)
		if !quoteAmount.IsZero() {
			// Use quote order quantity for market orders
			orderService = orderService.QuoteOrderQty(quoteAmount.String())
		} else {
			// Use regular quantity
			orderService = orderService.Quantity(amount.String())
		}
	default:
		return "", fmt.Errorf("unsupported order type: %s", orderType)
	}

	// Execute order
	b.logger.Printf("Creating order: %s %s %s amount=%s quoteAmount=%s price=%s",
		symbol, orderType, side, amount, quoteAmount, price)

	response, err := orderService.Do(ctx)
	if err != nil {
		return "", fmt.Errorf("failed to create order: %w", err)
	}

	b.logger.Printf("Order created: %+v", response)

	return response.ClientOrderID, nil
}

// GetOrderStatus returns the status of an order
func (b *BinanceExchange) GetOrderStatus(ctx context.Context, symbol, clientOrderID string) (*ExecutionReport, error) {
	order, err := b.client.NewGetOrderService().
		Symbol(symbol).
		OrigClientOrderID(clientOrderID).
		Do(ctx)

	if err != nil {
		return nil, fmt.Errorf("failed to get order status: %w", err)
	}

	// Convert to ExecutionReport
	return &ExecutionReport{
		ID:            int(order.OrderID),
		Symbol:        order.Symbol,
		Status:        string(order.Status),
		ClientOrderID: order.ClientOrderID,
		Side:          string(order.Side),
		Time:          time.Unix(order.Time/1000, 0),
		Info:          map[string]interface{}{"raw": order},
	}, nil
}

// GetTrade returns trade information for an order
func (b *BinanceExchange) GetTrade(ctx context.Context, symbol string, id int) (*Trade, error) {
	trades, err := b.client.NewListTradesService().
		Symbol(symbol).
		OrderId(int64(id)).
		Do(ctx)

	if err != nil {
		return nil, fmt.Errorf("failed to get trades: %w", err)
	}

	if len(trades) == 0 {
		return nil, nil
	}

	// Aggregate trade information
	qty := decimal.Zero
	quoteQty := decimal.Zero
	commissions := make(map[string]decimal.Decimal)
	prices := make([]decimal.Decimal, 0, len(trades))

	for _, trade := range trades {
		tradeQty, _ := decimal.NewFromString(trade.Quantity)
		price, _ := decimal.NewFromString(trade.Price)

		// Calculate quote quantity
		tradeQuoteQty := tradeQty.Mul(price)

		commission, _ := decimal.NewFromString(trade.Commission)

		qty = qty.Add(tradeQty)
		quoteQty = quoteQty.Add(tradeQuoteQty)

		if _, exists := commissions[trade.CommissionAsset]; !exists {
			commissions[trade.CommissionAsset] = decimal.Zero
		}
		commissions[trade.CommissionAsset] = commissions[trade.CommissionAsset].Add(commission)

		prices = append(prices, price)
	}

	// Calculate average price
	avgPrice := decimal.Zero
	if len(prices) > 0 {
		sum := decimal.Zero
		for _, price := range prices {
			sum = sum.Add(price)
		}
		avgPrice = sum.Div(decimal.NewFromInt(int64(len(prices))))
	}

	orderID := int(trades[0].OrderID)

	return &Trade{
		Symbol:          trades[0].Symbol,
		OrderID:         orderID,
		Qty:             qty,
		QuoteQty:        quoteQty,
		Commission:      decimal.Zero, // Not used, using Commissions map instead
		CommissionAsset: "",          // Not used, using Commissions map instead
		Commissions:     commissions,
		Price:           avgPrice,
	}, nil
}

// GetLotSize returns the lot size constraints for a symbol
func (b *BinanceExchange) GetLotSize(ctx context.Context, symbol string) (min, max, stepSize decimal.Decimal, err error) {
	// Get symbol info
	if len(b.symbolInfo) == 0 {
		if _, err := b.GetExchangeInfo(ctx); err != nil {
			return decimal.Zero, decimal.Zero, decimal.Zero, err
		}
	}

	b.mu.RLock()
	info, exists := b.symbolInfo[symbol]
	b.mu.RUnlock()

	if !exists {
		return decimal.Zero, decimal.Zero, decimal.Zero, fmt.Errorf("symbol %s not found", symbol)
	}

	return info.MinQty, info.MaxQty, info.StepSize, nil
}

// SplitSymbol splits a symbol into base and quote assets
func (b *BinanceExchange) SplitSymbol(symbol string) (baseAsset, quoteAsset string) {
	// Get symbol info
	b.mu.RLock()
	info, exists := b.symbolInfo[symbol]
	b.mu.RUnlock()

	if exists {
		return info.BaseAsset, info.QuoteAsset
	}

	// Fallback to common patterns if symbol info not available
	if len(symbol) > 3 {
		if strings.HasSuffix(symbol, "BTC") {
			return symbol[:len(symbol)-3], "BTC"
		} else if strings.HasSuffix(symbol, "ETH") {
			return symbol[:len(symbol)-3], "ETH"
		} else if strings.HasSuffix(symbol, "USDT") {
			return symbol[:len(symbol)-4], "USDT"
		} else if strings.HasSuffix(symbol, "BUSD") {
			return symbol[:len(symbol)-4], "BUSD"
		} else if strings.HasSuffix(symbol, "USDC") {
			return symbol[:len(symbol)-4], "USDC"
		}
	}

	// Default fallback
	return symbol[:len(symbol)/2], symbol[len(symbol)/2:]
}

// roundStepSize rounds quantity to step size
func (b *BinanceExchange) roundStepSize(quantity, stepSize decimal.Decimal) decimal.Decimal {
	if stepSize.IsZero() {
		return quantity
	}

	precision := 0
	stepSizeStr := stepSize.String()
	if strings.Contains(stepSizeStr, ".") {
		precision = len(stepSizeStr) - strings.IndexByte(stepSizeStr, '.') - 1
	}

	return quantity.Round(int32(precision))
}

// SetOrderUpdateCallback sets the callback for order updates
func (b *BinanceExchange) SetOrderUpdateCallback(callback func(*ExecutionReport)) {
	b.orderUpdateCallback = callback
}

// GetKlines returns klines (candlestick) data
func (b *BinanceExchange) getKlines(ctx context.Context, symbol, interval string, limit int, startTime, endTime int64) ([][]interface{}, error) {
	service := b.client.NewKlinesService().
		Symbol(symbol).
		Interval(interval).
		Limit(limit)

	if startTime > 0 {
		service = service.StartTime(startTime)
	}

	if endTime > 0 {
		service = service.EndTime(endTime)
	}

	klines, err := service.Do(ctx)
	if err != nil {
		return nil, err
	}

	// Convert to interface array for compatibility with mock exchange
	result := make([][]interface{}, len(klines))
	for i, kline := range klines {
		result[i] = []interface{}{
			kline.OpenTime,
			kline.Open,
			kline.High,
			kline.Low,
			kline.Close,
			kline.Volume,
			kline.CloseTime,
			kline.QuoteAssetVolume,
			kline.TradeNum,
			kline.TakerBuyBaseAssetVolume,
			kline.TakerBuyQuoteAssetVolume,
		}
	}

	return result, nil
}

// GetKlinesAndStore fetches historical klines data from Binance API
func (m *BinanceExchange) getKlinesAndStore(ctx context.Context, symbol string, startTime, endTime time.Time, interval string) (error) {
	m.logger.Printf("Fetching historical data for %s from %s to %s interval: %s",
		symbol, startTime.Format(time.RFC3339), endTime.Format(time.RFC3339), interval)

	// Convert to millisecond timestamps
	startMs := startTime.UnixNano() / int64(time.Millisecond)
	endMs := endTime.UnixNano() / int64(time.Millisecond)

	// Fetch klines from Binance
	klines, err := m.getKlines(ctx, symbol, interval, 1000, startMs, endMs)
	if err != nil {
		return err
	}

	dbKlines := make([]db.Kline, 0, len(klines))

	for _, kline := range klines {
		// Convert interface{} to int64
		openTime, ok := kline[0].(int64)
		if !ok {
			openTimeFloat, ok := kline[0].(float64)
			if ok {
				openTime = int64(openTimeFloat)
			} else {
				continue // Skip invalid data
			}
		}

		timestamp := time.Unix(0, openTime*int64(time.Millisecond))

		// Convert string values
		openStr, ok := kline[1].(string)
		if !ok {
			continue
		}

		highStr, ok := kline[2].(string)
		if !ok {
			continue
		}

		lowStr, ok := kline[3].(string)
		if !ok {
			continue
		}

		closeStr, ok := kline[4].(string)
		if !ok {
			continue
		}

		volumeStr, ok := kline[5].(string)
		if !ok {
			continue
		}

		// Parse additional fields for database storage
		quoteVolumeStr, _ := kline[7].(string)
		numberOfTrades, _ := kline[8].(float64)
		takerBuyBaseAssetStr, _ := kline[9].(string)
		takerBuyQuoteAssetStr, _ := kline[10].(string)

		open, _ := decimal.NewFromString(openStr)
		high, _ := decimal.NewFromString(highStr)
		low, _ := decimal.NewFromString(lowStr)
		close, _ := decimal.NewFromString(closeStr)
		volume, _ := decimal.NewFromString(volumeStr)
		quoteVolume, _ := decimal.NewFromString(quoteVolumeStr)
		takerBuyBaseAsset, _ := decimal.NewFromString(takerBuyBaseAssetStr)
		takerBuyQuoteAsset, _ := decimal.NewFromString(takerBuyQuoteAssetStr)


		dbKlines = append(dbKlines, db.Kline{
			Pair:               symbol,
			Interval:           interval,
			OpenTime:           timestamp,
			OpenPrice:          open,
			HighPrice:          high,
			LowPrice:           low,
			ClosePrice:         close,
			Volume:             volume,
			QuoteVolume:        quoteVolume,
			NumberOfTrades:     int(numberOfTrades),
			TakerBuyBaseAsset:  takerBuyBaseAsset,
			TakerBuyQuoteAsset: takerBuyQuoteAsset,
			Ignore:             false,
		})
	}

	// Store in database for future use
	if len(dbKlines) > 0 {
		if err := m.tradingDB.InsertKlinesBatch(ctx, dbKlines, 1000); err != nil {
			m.logger.Printf("Warning: Failed to store klines in database: %v", err)
			// Continue execution even if database storage fails
		} else {
			m.logger.Printf("Stored %d klines in database for %s", len(dbKlines), symbol)
		}
	}

	return nil
}


func (m *BinanceExchange) GetKlines(ctx context.Context, symbol, interval string, number int) ([]PriceDataPoint, error) {
	return m.GetKlinesWithCurrentTime(ctx, symbol, interval, number, time.Now())
}

// limit specifies the number of kline nodes to retrieve
func (m *BinanceExchange) GetKlinesWithCurrentTime(ctx context.Context, symbol, interval string, number int, currentTime time.Time) ([]PriceDataPoint, error) {
	batch := 1000

	if number > batch {
		log.Printf("Number of data points requested (%d) exceeds batch size (%d)", number, batch)
		panic("Number of data points requested exceeds batch size")
	}

	// Calculate time range
	// Adjust start time based on limit and interval to get approximately the right amount of data
	var duration time.Duration
	switch interval {
	case "1m":
		duration = time.Duration(number) * time.Minute
	case "5m":
		duration = time.Duration(number) * 5 * time.Minute
	case "15m":
		duration = time.Duration(number) * 15 * time.Minute
	case "30m":
		duration = time.Duration(number) * 30 * time.Minute
	case "1h":
		duration = time.Duration(number) * time.Hour
	case "4h":
		duration = time.Duration(number) * 4 * time.Hour
	case "1d":
		duration = time.Duration(number) * 24 * time.Hour
	default:
		log.Printf("Unsupported interval: %s", interval)
		panic("Unsupported interval")
	}
	startTime := currentTime.Add(-duration)
	endTime := currentTime

	// Query data from database
	klines, err := m.tradingDB.GetKlines(ctx, symbol, interval, startTime, endTime)
	if err != nil {
		m.logger.Printf("Error querying klines from database: %v", err)
		return nil, err
	}

	// If we don't have enough data in the database, fall back to fetching from Binance
	if len(klines) < number {
		//m.logger.Printf("Not enough data in database (%d), fetching from Binance for %s", len(klines), symbol)

		err := m.getKlinesAndStore(ctx, symbol, startTime, endTime, interval)
		if err != nil {
			m.logger.Printf("Error fetching klines from Binance: %v", err)
			return nil, err
		}

		klines, err = m.tradingDB.GetKlines(ctx, symbol, interval, startTime, endTime)
		if err != nil {
			m.logger.Printf("Error querying klines from database: %v", err)
			return nil, err
		}
	}

	if len(klines) < number {
		m.logger.Printf("Not enough data in database (%d), even after fetching from Binance", len(klines))
		return nil, fmt.Errorf("not enough data in database")
	}

	// Convert database klines to PriceDataPoint
	dataPoints := make([]PriceDataPoint, 0, len(klines))
	for _, kline := range klines {
		dataPoints = append(dataPoints, PriceDataPoint{
			Timestamp: kline.OpenTime,
			Open:      kline.OpenPrice,
			High:      kline.HighPrice,
			Low:       kline.LowPrice,
			Close:     kline.ClosePrice,
			Volume:    kline.Volume,
		})
	}

	return dataPoints, nil
}