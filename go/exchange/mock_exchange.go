package exchange

import (
	"context"
	"fmt"
	"os"
	"path/filepath"
	"sync"
	"time"

	"github.com/kenny/trading-bot/go/db"
	"github.com/kenny/trading-bot/go/utils"
	"github.com/shopspring/decimal"
)

// PriceDataPoint represents a single point of historical price data
type PriceDataPoint struct {
	Timestamp time.Time
	Open      decimal.Decimal
	High      decimal.Decimal
	Low       decimal.Decimal
	Close     decimal.Decimal
	Volume    decimal.Decimal
}

// MockExchange implements the Exchange interface for backtesting
// It extends BinanceExchange to inherit its functionality
type MockExchange struct {
	*BinanceExchange
	balances         map[string]AssetBalance
	currentTime      time.Time
	endTime          time.Time
	orders           map[string]map[string]interface{}
	dataDir          string
	mockMu           sync.RWMutex // Separate mutex for mock-specific operations
}

// NewMockExchange creates a new instance of MockExchange
func NewMockExchange(apiKey, secretKey string, startTime, endTime time.Time, tradingDB *db.TradingDB) *MockExchange {
	// Create the base BinanceExchange
	binanceExchange := NewBinanceExchange(apiKey, secretKey, tradingDB)

	// Override the logger
	logger := utils.NewLogger("MOCK-EXCHANGE")
	binanceExchange.logger = logger

	// Create data directory
	dataDir := filepath.Join("data")
	os.MkdirAll(dataDir, os.ModePerm)

	// Initialize with default balances
	balances := make(map[string]AssetBalance)

	// TODO: Load initial balances from config
	// For now, hardcode some test balances
	balances["BTC"] = AssetBalance{
		Asset: "BTC",
		Free:  decimal.NewFromFloat(1.0),
		Locked: decimal.Zero,
	}
	balances["USDT"] = AssetBalance{
		Asset: "USDT",
		Free:  decimal.NewFromFloat(10000.0),
		Locked: decimal.Zero,
	}

	return &MockExchange{
		BinanceExchange:   binanceExchange,
		balances:          balances,
		currentTime:       startTime,
		endTime:           endTime,
		orders:            make(map[string]map[string]interface{}),
		dataDir:           dataDir,
	}
}

// Start initializes the mock exchange
func (m *MockExchange) Start(ctx context.Context) error {
	m.logger.Info("Starting MockExchange...")
	m.logger.Infof("Backtest period: %s to %s", m.currentTime.Format(time.RFC3339), m.endTime.Format(time.RFC3339))

    // Use the underlying BinanceExchange to fetch data
	if err := m.BinanceExchange.Start(ctx); err != nil {
		panic(err)
	}
	return nil
}

// Stop cleans up resources
func (m *MockExchange) Stop(ctx context.Context) error {
	m.logger.Info("Stopping MockExchange...")
	return nil
}

// GetExchangeInfo returns exchange information
// This method uses the underlying BinanceExchange implementation
func (m *MockExchange) GetExchangeInfo(ctx context.Context) (map[string]SymbolInfo, error) {
	return m.BinanceExchange.GetExchangeInfo(ctx)
}

// CreateOrder creates a new order
func (m *MockExchange) CreateOrder(ctx context.Context, symbol, orderType, side string, amount, quoteAmount, price decimal.Decimal) (string, error) {
	m.logger.Infof("Creating mock order: %s %s %s amount=%s quoteAmount=%s price=%s",
		symbol, orderType, side, amount, quoteAmount, price)

	// Generate order ID if not provided
	clientOrderID := fmt.Sprintf("mock-%d", time.Now().UnixNano())

	// Store order in memory
	m.mockMu.Lock()
	m.orders[clientOrderID] = map[string]interface{}{
		"symbol":     symbol,
		"orderType":  orderType,
		"side":       side,
		"amount":     amount,
		"quoteAmount": quoteAmount,
		"price":      price,
		"status":     "NEW",
		"time":       m.currentTime,
	}
	m.mockMu.Unlock()

	// Simulate order execution
	go func() {
		// Wait a bit to simulate network delay
		time.Sleep(100 * time.Millisecond)

		// Update order status to FILLED
		m.mockMu.Lock()
		if order, exists := m.orders[clientOrderID]; exists {
			order["status"] = "FILLED"
			order["fillTime"] = m.currentTime.Add(time.Second)
			m.orders[clientOrderID] = order
		}
		m.mockMu.Unlock()

		// Call order update callback if set
		if m.orderUpdateCallback != nil {
			report := &ExecutionReport{
				ID:            int(time.Now().UnixNano() % 1000000),
				Symbol:        symbol,
				Status:        "FILLED",
				ClientOrderID: clientOrderID,
				Side:          side,
				Time:          m.currentTime,
				Info:          m.orders[clientOrderID],
			}
			m.orderUpdateCallback(report)
		}
	}()

	return clientOrderID, nil
}

// GetOrderStatus returns the status of an order
func (m *MockExchange) GetOrderStatus(ctx context.Context, symbol, clientOrderID string) (*ExecutionReport, error) {
	m.mockMu.RLock()
	defer m.mockMu.RUnlock()

	order, exists := m.orders[clientOrderID]
	if !exists {
		return nil, fmt.Errorf("order not found: %s", clientOrderID)
	}

	return &ExecutionReport{
		ID:            int(time.Now().UnixNano() % 1000000),
		Symbol:        symbol,
		Status:        order["status"].(string),
		ClientOrderID: clientOrderID,
		Side:          order["side"].(string),
		Time:          order["time"].(time.Time),
		Info:          order,
	}, nil
}

// GetTrade returns trade information for an order
func (m *MockExchange) GetTrade(ctx context.Context, symbol string, id int) (*Trade, error) {
	m.mockMu.RLock()
	defer m.mockMu.RUnlock()

	// Find order by ID
	var orderInfo map[string]interface{}

	for _, order := range m.orders {
		if order["symbol"] == symbol {
			orderInfo = order
			break
		}
	}

	if orderInfo == nil {
		return nil, fmt.Errorf("trade not found for order ID: %d", id)
	}

	// Get current price
	price, err := m.GetCurrentPrice(ctx, symbol)
	if err != nil {
		return nil, err
	}

	// Calculate quantities
	var qty, quoteQty decimal.Decimal
	if orderInfo["side"].(string) == "BUY" {
		qty = orderInfo["amount"].(decimal.Decimal)
		quoteQty = qty.Mul(price)
	} else {
		quoteQty = orderInfo["amount"].(decimal.Decimal)
		qty = quoteQty.Div(price)
	}

	// Simulate commission (0.1%)
	commission := quoteQty.Mul(decimal.NewFromFloat(0.001))
	commissions := map[string]decimal.Decimal{
		"BNB": commission,
	}

	return &Trade{
		Symbol:          symbol,
		OrderID:         id,
		Qty:             qty,
		QuoteQty:        quoteQty,
		Commission:      decimal.Zero,
		CommissionAsset: "",
		Commissions:     commissions,
		Price:           price,
	}, nil
}

// GetLotSize returns the lot size constraints for a symbol
func (m *MockExchange) GetLotSize(ctx context.Context, symbol string) (min, max, stepSize decimal.Decimal, err error) {
	// Use the BinanceExchange implementation
	return m.BinanceExchange.GetLotSize(ctx, symbol)
}

// SplitSymbol splits a symbol into base and quote assets
func (m *MockExchange) SplitSymbol(symbol string) (baseAsset, quoteAsset string) {
	// Use the BinanceExchange implementation
	return m.BinanceExchange.SplitSymbol(symbol)
}

// GetCurrentPrice returns the current price for a symbol
func (m *MockExchange) GetCurrentPrice(ctx context.Context, symbol string) (decimal.Decimal, error) {
	// Get historical data for the symbol
	dataPoints, err := m.GetKlinesWithCurrentTime(ctx, symbol, "1m", 100, m.currentTime)
	if err != nil || len(dataPoints) == 0 {
		// panic
		panic("No price data available")
	}

	// Use the most recent price
	latestPrice := dataPoints[len(dataPoints)-1].Close

	return latestPrice, nil
}


// GetHistoricalPrices returns historical closing prices for a symbol
func (m *MockExchange) GetHistoricalPrices(ctx context.Context, symbol string, lookbackPeriods int, interval string) ([]decimal.Decimal, error) {
	// Get historical data
	dataPoints, err := m.GetKlinesWithCurrentTime(ctx, symbol, interval, lookbackPeriods, m.currentTime)
	if err != nil {
		return nil, err
	}

    // Extract closing prices
    prices := make([]decimal.Decimal, 0, lookbackPeriods)
    for i := len(dataPoints) - lookbackPeriods; i < len(dataPoints); i++ {
        if i >= 0 {
            prices = append(prices, dataPoints[i].Close)
        }
    }

    // Pad with zeros if we don't have enough data
    for len(prices) < lookbackPeriods {
        prices = append([]decimal.Decimal{decimal.Zero}, prices...)
    }

    return prices, nil
}

// GetBalance returns the account balance
func (m *MockExchange) GetBalance(ctx context.Context, currency string) (*Balance, error) {
	m.mockMu.RLock()
	defer m.mockMu.RUnlock()

	balances := make(map[string]AssetBalance)

	if currency == "" {
		// Return all balances
		for k, v := range m.balances {
			balances[k] = v
		}
	} else {
		// Return specific currency
		if balance, exists := m.balances[currency]; exists {
			balances[currency] = balance
		}
	}

	return &Balance{
		Balances:    balances,
		UpdateTime:  m.currentTime,
		AccountType: "SPOT",
		Permissions: []string{"SPOT"},
	}, nil
}

// GetBalanceTotal returns the total balance in USDT
func (m *MockExchange) GetBalanceTotal(ctx context.Context) (decimal.Decimal, error) {
	balance, err := m.GetBalance(ctx, "")
	if err != nil {
		return decimal.Zero, err
	}

	total := decimal.Zero

	for asset, assetBalance := range balance.Balances {
		if asset == "USDT" {
			total = total.Add(assetBalance.Free.Add(assetBalance.Locked))
			continue
		}

		// Get price in USDT
		symbol := asset + "USDT"
		price, err := m.GetCurrentPrice(ctx, symbol)
		if err != nil {
			// Skip assets that don't have a USDT pair
			continue
		}

		assetValue := assetBalance.Free.Add(assetBalance.Locked).Mul(price)
		total = total.Add(assetValue)
	}

	return total, nil
}

// SetOrderUpdateCallback sets the callback for order updates
func (m *MockExchange) SetOrderUpdateCallback(callback func(*ExecutionReport)) {
	m.orderUpdateCallback = callback
}

// AdvanceTime advances the current time for backtesting
// If timeStep is 0, uses a default time step of 1 minute
// Returns true if the time was advanced, false if the end time was reached
func (m *MockExchange) AdvanceTime(ctx context.Context, timeStep time.Duration) (bool, error) {
	m.mockMu.Lock()
	defer m.mockMu.Unlock()

	if timeStep == 0 {
		timeStep = time.Minute // Default time step
	}

	newTime := m.currentTime.Add(timeStep)

	if newTime.After(m.endTime) {
		return false, nil
	}

	m.currentTime = newTime
	m.logger.Infof("Advanced time to %s", m.currentTime.Format(time.RFC3339))
	return true, nil
}
