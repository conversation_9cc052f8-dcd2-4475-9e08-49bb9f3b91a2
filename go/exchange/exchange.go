package exchange

import (
	"context"
	"time"

	"github.com/shopspring/decimal"
)

// Exchange defines the interface for interacting with cryptocurrency exchanges
type Exchange interface {
    // Core methods
    Start(ctx context.Context) error
    Stop(ctx context.Context) error
    
    // Market data methods
    GetTicker(ctx context.Context, symbol string) (*Ticker, error)
    GetExchangeInfo(ctx context.Context) (map[string]SymbolInfo, error)
    GetHistoricalPrices(ctx context.Context, symbol string, lookbackPeriods int, interval string) ([]decimal.Decimal, error)
    GetCurrentPrice(ctx context.Context, symbol string) (decimal.Decimal, error)
    
    // Account methods
    GetBalance(ctx context.Context, currency string) (*Balance, error)
    GetBalanceTotal(ctx context.Context) (decimal.Decimal, error)
    
    // Order methods
    CreateOrder(ctx context.Context, symbol, orderType, side string, amount, quoteAmount, price decimal.Decimal) (string, error)
    GetOrderStatus(ctx context.Context, symbol, clientOrderID string) (*ExecutionReport, error)
    GetTrade(ctx context.Context, symbol string, id int) (*Trade, error)
    
    // Utility methods
    GetLotSize(ctx context.Context, symbol string) (min, max, stepSize decimal.Decimal, err error)
    SplitSymbol(symbol string) (baseAsset, quoteAsset string)
}

// Ticker represents market ticker information
type Ticker struct {
    Symbol           string
    PriceChange      decimal.Decimal
    PriceChangePercent decimal.Decimal
    WeightedAvgPrice decimal.Decimal
    PrevClosePrice   decimal.Decimal
    LastPrice        decimal.Decimal
    BidPrice         decimal.Decimal
    AskPrice         decimal.Decimal
    OpenPrice        decimal.Decimal
    HighPrice        decimal.Decimal
    LowPrice         decimal.Decimal
    Volume           decimal.Decimal
    QuoteVolume      decimal.Decimal
    OpenTime         time.Time
    CloseTime        time.Time
}

// SymbolInfo contains information about a trading pair
type SymbolInfo struct {
    BaseAsset          string
    QuoteAsset         string
    BaseAssetPrecision int
    QuoteAssetPrecision int
    Status             string
    MinPrice           decimal.Decimal
    MaxPrice           decimal.Decimal
    TickSize           decimal.Decimal
    MinQty             decimal.Decimal
    MaxQty             decimal.Decimal
    StepSize           decimal.Decimal
}

// AssetBalance represents the balance of a single asset
type AssetBalance struct {
    Asset  string
    Free   decimal.Decimal
    Locked decimal.Decimal
}

// Balance represents account balance information
type Balance struct {
    Balances     map[string]AssetBalance
    UpdateTime   time.Time
    AccountType  string
    Permissions  []string
}

// ExecutionReport contains information about an order's execution
type ExecutionReport struct {
    ID            int
    Symbol        string
    Status        string
    ClientOrderID string
    Side          string
    Time          time.Time
    Info          map[string]interface{}
}

// Trade represents information about a completed trade
type Trade struct {
    Symbol         string
    OrderID        int
    Qty            decimal.Decimal
    QuoteQty       decimal.Decimal
    Commission     decimal.Decimal
    CommissionAsset string
    Commissions    map[string]decimal.Decimal
    Price          decimal.Decimal
}