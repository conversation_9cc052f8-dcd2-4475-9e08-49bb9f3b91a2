package db

import (
	"context"
	"database/sql"
	"fmt"
	"time"

	_ "github.com/lib/pq" // PostgreSQL driver
	"github.com/shopspring/decimal"
	"github.com/yourusername/yourproject/utils"
)

// ReactToMarketCapBalance represents a current balance record
type ReactToMarketCapBalance struct {
	ID          int64
	Pair        string
	LongTerm    int
	ShortTerm   int
	OrderID     *string // nullable
	Amount      decimal.Decimal
	HoldingBase bool
	InFlight    bool
	LastPrice   *decimal.Decimal // nullable
	UpdatedAt   time.Time
}

// CoinValue represents a coin value record
type CoinValue struct {
	ID            int64
	BalanceID     int64
	Coin          string
	LastAmount    decimal.Decimal
	CreatedAt     time.Time
	UpdatedAt     time.Time
}

// Order represents an order record
type Order struct {
	ID            int64
	BalanceID     int64
	Symbol        string
	Side          string
	OrderID       string
	ClientOrderID string
	Status        string
	CreatedAt     time.Time
	UpdatedAt     time.Time
}

// TradingDB handles database operations for trading
type TradingDB struct {
	db     *sql.DB
	logger *utils.Logger
}

// NewTradingDB creates a new TradingDB instance
func NewTradingDB(host, port, dbname, user, password string, logger *utils.Logger) (*TradingDB, error) {
	// Create PostgreSQL connection string
	connStr := fmt.Sprintf("host=%s port=%s dbname=%s user=%s password=%s sslmode=disable",
		host, port, dbname, user, password)

	// Connect to PostgreSQL
	db, err := sql.Open("postgres", connStr)
	if err != nil {
		return nil, fmt.Errorf("failed to open database: %w", err)
	}

	// Set connection pool settings
	db.SetMaxOpenConns(10)
	db.SetMaxIdleConns(5)
	db.SetConnMaxLifetime(time.Hour)

	// Test connection
	if err := db.Ping(); err != nil {
		db.Close()
		return nil, fmt.Errorf("failed to ping database: %w", err)
	}

	logger.Info("Connected to PostgreSQL database")

	return &TradingDB{
		db:     db,
		logger: logger,
	}, nil
}

// Close closes the database connection
func (t *TradingDB) Close() error {
	return t.db.Close()
}

// ReactToMarketCapGetAllCurrentBalances returns all current balances
func (t *TradingDB) ReactToMarketCapGetAllCurrentBalances(ctx context.Context) ([]ReactToMarketCapBalance, error) {
	query := `
		SELECT id, pair, long_term, short_term, order_id, amount, holding_base, in_flight, last_price, updated_at
		FROM react_to_market_cap_current_balance
	`

	rows, err := t.db.QueryContext(ctx, query)
	if err != nil {
		return nil, fmt.Errorf("failed to query current balances: %w", err)
	}
	defer rows.Close()

	var balances []ReactToMarketCapBalance
	for rows.Next() {
		var b ReactToMarketCapBalance
		var lastPriceNull sql.NullFloat64
		var updatedAt time.Time

		err := rows.Scan(&b.ID, &b.Pair, &b.LongTerm, &b.ShortTerm, &b.OrderID, &b.Amount, &b.HoldingBase, &b.InFlight, &lastPriceNull, &updatedAt)
		if err != nil {
			return nil, fmt.Errorf("failed to scan balance row: %w", err)
		}

		b.UpdatedAt = updatedAt

		if lastPriceNull.Valid {
			price := decimal.NewFromFloat(lastPriceNull.Float64)
			b.LastPrice = &price
		} else {
			b.LastPrice = nil
		}

		balances = append(balances, b)
	}

	if err := rows.Err(); err != nil {
		return nil, fmt.Errorf("error iterating balance rows: %w", err)
	}

	return balances, nil
}

// ReactToMarketCapUpdateCurrentBalance updates or creates a current balance
func (t *TradingDB) ReactToMarketCapUpdateCurrentBalance(ctx context.Context, pair string, longTerm, shortTerm int, amount decimal.Decimal) (int64, error) {
	query := `
		INSERT INTO react_to_market_cap_current_balance
		(pair, long_term, short_term, amount)
		VALUES ($1, $2, $3, $4)
		RETURNING id
	`

	var id int64
	err := t.db.QueryRowContext(ctx, query, pair, longTerm, shortTerm, amount).Scan(&id)
	if err != nil {
		return 0, fmt.Errorf("failed to insert current balance: %w", err)
	}

	return id, nil
}

// UpdateLastCoinValue updates or creates a coin value record
func (t *TradingDB) UpdateLastCoinValue(ctx context.Context, balanceID int64, coin string, amount decimal.Decimal) error {
	query := `
		INSERT INTO mean_reversion_last_coin_value
		(current_balance_id, last_coin, last_amount)
		VALUES ($1, $2, $3)
		ON CONFLICT (current_balance_id, last_coin) DO UPDATE
		SET last_amount = $3,
			updated_at = CURRENT_TIMESTAMP
	`

	_, err := t.db.ExecContext(ctx, query, balanceID, coin, amount)
	if err != nil {
		return fmt.Errorf("failed to insert coin value: %w", err)
	}

	return nil
}

// GetLastCoinValue gets the last coin value for a balance and coin
func (t *TradingDB) GetLastCoinValue(ctx context.Context, balanceID int64, coin string) (*CoinValue, error) {
	query := `
		SELECT id, current_balance_id, last_coin, last_amount, updated_at
		FROM mean_reversion_last_coin_value
		WHERE current_balance_id = $1 AND last_coin = $2
	`

	var cv CoinValue
	var updatedAt time.Time

	err := t.db.QueryRowContext(ctx, query, balanceID, coin).Scan(
		&cv.ID, &cv.BalanceID, &cv.Coin, &cv.LastAmount, &updatedAt,
	)
	if err != nil {
		if err == sql.ErrNoRows {
			return nil, nil
		}
		return nil, fmt.Errorf("failed to query last coin value: %w", err)
	}

	cv.UpdatedAt = updatedAt

	return &cv, nil
}

// CreateOrder creates a new order record
func (t *TradingDB) CreateOrder(ctx context.Context, balanceID int64, symbol, side, clientOrderID string) (int64, error) {
	// First, update the balance to mark it as in-flight
	updateQuery := `
		UPDATE react_to_market_cap_current_balance
		SET order_id = $1,
			in_flight = TRUE,
			updated_at = CURRENT_TIMESTAMP
		WHERE id = $2
	`

	_, err := t.db.ExecContext(ctx, updateQuery, clientOrderID, balanceID)
	if err != nil {
		return 0, fmt.Errorf("failed to update balance for order: %w", err)
	}

	// Return the balance ID as the order ID for simplicity
	return balanceID, nil
}

// UpdateOrderStatus updates an order's status
func (t *TradingDB) UpdateOrderStatus(ctx context.Context, clientOrderID, status, orderID string) error {
	// In PostgreSQL implementation, we don't have a separate orders table
	// Instead, we update the react_to_market_cap_current_balance table
	if status == "FILLED" {
		// Order is filled, but we'll update the balance in UpdateBalanceAfterOrderCompletion
		return nil
	}

	// For other statuses, just update the in_flight status if needed
	if status == "CANCELED" || status == "REJECTED" {
		query := `
			UPDATE react_to_market_cap_current_balance
			SET in_flight = FALSE,
				order_id = NULL,
				updated_at = CURRENT_TIMESTAMP
			WHERE order_id = $1
		`

		_, err := t.db.ExecContext(ctx, query, clientOrderID)
		if err != nil {
			return fmt.Errorf("failed to update order status: %w", err)
		}
	}

	return nil
}

// UpdateBalanceAfterOrderCompletion updates a balance after an order is completed
func (t *TradingDB) UpdateBalanceAfterOrderCompletion(ctx context.Context, id int64, amount decimal.Decimal, holdingBase bool, lastPrice decimal.Decimal) error {
	query := `
		UPDATE react_to_market_cap_current_balance
		SET order_id = NULL,
			in_flight = FALSE,
			holding_base = $1,
			amount = $2,
			last_price = $3,
			updated_at = CURRENT_TIMESTAMP
		WHERE id = $4
	`

	_, err := t.db.ExecContext(ctx, query, holdingBase, amount, lastPrice, id)
	if err != nil {
		return fmt.Errorf("failed to update balance after order completion: %w", err)
	}

	return nil
}

// Kline represents a kline (candlestick) data point
type Kline struct {
	ID                  int64
	Pair                string
	Interval            string
	OpenTime            time.Time
	OpenPrice           decimal.Decimal
	HighPrice           decimal.Decimal
	LowPrice            decimal.Decimal
	ClosePrice          decimal.Decimal
	Volume              decimal.Decimal
	QuoteVolume         decimal.Decimal
	NumberOfTrades      int
	TakerBuyBaseAsset   decimal.Decimal
	TakerBuyQuoteAsset  decimal.Decimal
	Ignore              bool
}

// GetKlines retrieves klines data from the database
func (t *TradingDB) GetKlines(ctx context.Context, pair, interval string, startTime, endTime time.Time) ([]Kline, error) {
	query := `
		SELECT id, pair, interval, open_time, open_price, high_price, low_price, close_price,
		       volume, quote_volume, number_of_trades, taker_buy_base_asset, taker_buy_quote_asset, ignore
		FROM klines
		WHERE pair = $1 AND interval = $2 AND open_time >= $3 AND open_time <= $4
		ORDER BY open_time ASC
	`

	rows, err := t.db.QueryContext(ctx, query, pair, interval, startTime, endTime)
	if err != nil {
		return nil, fmt.Errorf("failed to query klines: %w", err)
	}
	defer rows.Close()

	var klines []Kline
	for rows.Next() {
		var k Kline
		err := rows.Scan(
			&k.ID, &k.Pair, &k.Interval, &k.OpenTime, &k.OpenPrice, &k.HighPrice, &k.LowPrice, &k.ClosePrice,
			&k.Volume, &k.QuoteVolume, &k.NumberOfTrades, &k.TakerBuyBaseAsset, &k.TakerBuyQuoteAsset, &k.Ignore,
		)
		if err != nil {
			return nil, fmt.Errorf("failed to scan kline row: %w", err)
		}
		klines = append(klines, k)
	}

	if err := rows.Err(); err != nil {
		return nil, fmt.Errorf("error iterating kline rows: %w", err)
	}

	return klines, nil
}

// InsertKlines inserts klines data with conflict resolution
func (t *TradingDB) InsertKlines(ctx context.Context, klines []Kline) error {
	if len(klines) == 0 {
		return nil
	}

	query := `
		INSERT INTO klines
		(pair, interval, open_time, open_price, high_price, low_price, close_price, volume, quote_volume, number_of_trades, taker_buy_base_asset, taker_buy_quote_asset, ignore)
		VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13)
		ON CONFLICT (pair, interval, open_time)
		DO UPDATE SET
			open_price = EXCLUDED.open_price,
			high_price = EXCLUDED.high_price,
			low_price = EXCLUDED.low_price,
			close_price = EXCLUDED.close_price,
			volume = EXCLUDED.volume,
			quote_volume = EXCLUDED.quote_volume,
			number_of_trades = EXCLUDED.number_of_trades,
			taker_buy_base_asset = EXCLUDED.taker_buy_base_asset,
			taker_buy_quote_asset = EXCLUDED.taker_buy_quote_asset,
			ignore = EXCLUDED.ignore
	`

	// Prepare statement for better performance with multiple inserts
	stmt, err := t.db.PrepareContext(ctx, query)
	if err != nil {
		return fmt.Errorf("failed to prepare klines insert statement: %w", err)
	}
	defer stmt.Close()

	// Insert each kline
	for _, kline := range klines {
		_, err := stmt.ExecContext(ctx,
			kline.Pair, kline.Interval, kline.OpenTime, kline.OpenPrice, kline.HighPrice, kline.LowPrice, kline.ClosePrice,
			kline.Volume, kline.QuoteVolume, kline.NumberOfTrades, kline.TakerBuyBaseAsset, kline.TakerBuyQuoteAsset, kline.Ignore,
		)
		if err != nil {
			return fmt.Errorf("failed to insert kline: %w", err)
		}
	}

	return nil
}

// InsertKlinesBatch inserts klines data in batches for better performance
func (t *TradingDB) InsertKlinesBatch(ctx context.Context, klines []Kline, batchSize int) error {
	if len(klines) == 0 {
		return nil
	}

	if batchSize <= 0 {
		batchSize = 1000 // Default batch size
	}

	for i := 0; i < len(klines); i += batchSize {
		end := i + batchSize
		if end > len(klines) {
			end = len(klines)
		}

		batch := klines[i:end]
		if err := t.InsertKlines(ctx, batch); err != nil {
			return fmt.Errorf("failed to insert klines batch %d-%d: %w", i, end-1, err)
		}

		t.logger.Printf("Inserted klines batch %d-%d (%d records)", i, end-1, len(batch))
	}

	return nil
}


// ReactToMarketCapCurrentBalance represents a current balance record
type ReactToMarketCapCurrentBalance struct {
	ID          int64
	Pair        string
	LongTerm    int
	ShortTerm   int
	OrderID     *string // nullable
	HoldingBase bool
	InFlight    bool
	Amount      decimal.Decimal
	LastPrice   *decimal.Decimal // nullable
	UpdatedAt   time.Time
}

// ReactToMarketCapCurrentBalanceOrderPlaced updates a balance when an order is placed
func (t *TradingDB) ReactToMarketCapCurrentBalanceOrderPlaced(ctx context.Context, id int64, orderID string) error {
	query := `
		UPDATE react_to_market_cap_current_balance
		SET order_id = $1,
			in_flight = TRUE,
			updated_at = CURRENT_TIMESTAMP
		WHERE id = $2
	`

	_, err := t.db.ExecContext(ctx, query, orderID, id)
	if err != nil {
		return fmt.Errorf("failed to update react_to_market_cap_current_balance order placed: %w", err)
	}

	return nil
}

// ReactToMarketCapCurrentBalanceOrderCompleted updates a balance when an order is completed
func (t *TradingDB) ReactToMarketCapCurrentBalanceOrderCompleted(ctx context.Context, id int64, amount decimal.Decimal, holdingBase bool, lastPrice decimal.Decimal) error {
	query := `
		UPDATE react_to_market_cap_current_balance
		SET order_id = NULL,
			in_flight = FALSE,
			holding_base = $1,
			amount = $2,
			last_price = $3,
			updated_at = CURRENT_TIMESTAMP
		WHERE id = $4
	`

	_, err := t.db.ExecContext(ctx, query, holdingBase, amount, lastPrice, id)
	if err != nil {
		return fmt.Errorf("failed to update react_to_market_cap_current_balance order completed: %w", err)
	}

	return nil
}

// ReactToMarketCapDeductBNBFeeFromBalances deducts a BNB fee from a balance
func (t *TradingDB) ReactToMarketCapDeductBNBFeeFromBalances(ctx context.Context, id int64, fee decimal.Decimal) error {
	query := `
		UPDATE react_to_market_cap_current_balance
		SET amount = amount - $1,
			updated_at = CURRENT_TIMESTAMP
		WHERE id = $2
	`

	_, err := t.db.ExecContext(ctx, query, fee, id)
	if err != nil {
		return fmt.Errorf("failed to deduct BNB fee from react_to_market_cap_current_balance: %w", err)
	}

	return nil
}
