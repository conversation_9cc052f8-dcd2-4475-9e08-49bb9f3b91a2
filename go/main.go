package main

import (
	"context"
	"fmt"
	"os"
	"os/signal"
	"syscall"
	"time"

	"github.com/kenny/trading-bot/go/db"
	"github.com/kenny/trading-bot/go/exchange"
	"github.com/kenny/trading-bot/go/strategies"
	"github.com/kenny/trading-bot/go/utils"
	"github.com/shopspring/decimal"
)

func main() {
	// Setup logger
	logger := utils.NewLogger("TRADING-BOT")
	defer func() {
		if err := logger.Close(); err != nil {
			// Use standard log for this since our logger might be closed
			fmt.Printf("Error closing logger: %v\n", err)
		}
	}()

	// Create context with cancellation
	ctx, cancel := context.WithCancel(context.Background())
	defer cancel()

	// Handle graceful shutdown
	sigChan := make(chan os.Signal, 1)
	signal.Notify(sigChan, syscall.SIGINT, syscall.SIGTERM)
	go func() {
		<-sig<PERSON>han
		logger.Info("Shutting down...")
		cancel()
	}()

	// Load configuration
	config := utils.NewConfig()

	// Initialize database first
	tradingDB, err := db.NewTradingDB(
		config.DBHost,
		config.DBPort,
		config.DBName,
		config.DBUser,
		config.DBPassword,
		utils.NewLogger("DB"),
	)
	if err != nil {
		logger.Fatalf("Failed to initialize database: %v", err)
	}
	defer tradingDB.Close()
	logger.Infof("Database initialized: PostgreSQL at %s:%s/%s", config.DBHost, config.DBPort, config.DBName)

	// Create exchange based on mode
	var ex exchange.Exchange
	if config.TradingMode == "backtest" {
		// Initialize mock exchange for backtesting
		ex = exchange.NewMockExchange(
			config.ExchangeAPIKey,
			config.ExchangeSecretKey,
			config.BacktestStartTime,
			config.BacktestEndTime,
			tradingDB,
		)
		logger.Info("Using MockExchange for backtesting")
	} else {
		// Initialize Binance exchange for live trading
		ex = exchange.NewBinanceExchange(
			config.ExchangeAPIKey,
			config.ExchangeSecretKey,
			tradingDB,
		)
		logger.Info("Using BinanceExchange for live trading")
	}

	// Initialize exchange
	if err := ex.Start(ctx); err != nil {
		logger.Fatalf("Failed to start exchange: %v", err)
	}
	defer ex.Stop(ctx)

	// Initialize Telegram client
	telegram := utils.NewTelegramClient()

	// Create strategy
	strategy := strategies.NewReactToMarketCapStrategy(ex, logger, tradingDB, telegram)
	if err := strategy.Start(ctx); err != nil {
		strategy.UnrecoverableErrorf(ctx, "Failed to start strategy: %v", err)
	}
	defer strategy.Stop(ctx)

	// Run strategy
	if config.TradingMode == "backtest" {
		runBacktest(ctx, strategy, ex.(*exchange.MockExchange), logger)
	} else {
		runLiveTrading(ctx, strategy, logger)
	}
}

func runBacktest(ctx context.Context, strategy strategies.Strategy, mockExchange *exchange.MockExchange, logger *utils.Logger) {
	logger.Info("Starting backtest simulation...")

	// Get initial balance for comparison
	initialBalance, err := mockExchange.GetBalanceTotal(ctx)
	if err != nil {
		logger.Fatalf("Failed to get initial balance: %v", err)
	}
	logger.Infof("Starting with balance: %v", initialBalance)

	// Run backtest until completion or cancellation
	for {
		select {
		case <-ctx.Done():
			logger.Info("Backtest cancelled")
			return
		default:
            if !strategy.Running() {
                logger.Info("Strategy not running, exiting backtest")
                return
            }
			// Run strategy iteration
			if err := strategy.Loop(ctx); err != nil {
                strategy.UnrecoverableErrorf(ctx, "Strategy error: %v", err)
                return
			}

			// Advance time for backtest
			continueBacktest, err := mockExchange.AdvanceTime(ctx, 0) // Use default time step
			if err != nil {
				logger.Fatalf("Error advancing time: %v", err)
			}

			if !continueBacktest {
				logger.Info("Backtest completed - reached end time")

				// Print final results
				finalBalance, err := mockExchange.GetBalanceTotal(ctx)
				if err != nil {
					logger.Fatalf("Failed to get final balance: %v", err)
				}

				profitPercentage := finalBalance.Sub(initialBalance).Div(initialBalance).Mul(decimal.NewFromInt(100))
				logger.Info("Backtest Results:")
				logger.Infof("End with balance: %v", finalBalance)
				logger.Infof("Profit: %s%%", profitPercentage.StringFixed(2))
				return
			}
		}
	}
}

func runLiveTrading(ctx context.Context, strategy strategies.Strategy, logger *utils.Logger) {
	logger.Info("Starting live trading...")

	ticker := time.NewTicker(time.Second)
	defer ticker.Stop()

	for {
		select {
		case <-ctx.Done():
			logger.Info("Live trading stopped")
			return
		case <-ticker.C:
            // check if state is running
            if !strategy.Running() {
                logger.Info("Strategy not running, skipping loop")
                continue
            }
			if err := strategy.Loop(ctx); err != nil {
				strategy.UnrecoverableErrorf(ctx, "Strategy error: %v", err)
			}
		}
	}
}

