package main

import (
	"os"
	"time"

	"github.com/kenny/trading-bot/go/utils"
)

func main() {
	// Set environment variables for demonstration
	os.Setenv("LOG_DIR", "./example_logs")
	os.Setenv("LOG_LEVEL", "debug")
	os.Setenv("LOG_MAX_SIZE", "1")      // 1MB for quick rotation demo
	os.Setenv("LOG_MAX_BACKUPS", "3")   // Keep 3 backup files
	os.Setenv("LOG_MAX_AGE", "7")       // Keep logs for 7 days
	os.Setenv("LOG_COMPRESS", "true")   // Compress old logs

	// Create logger
	logger := utils.NewLogger("EXAMPLE")
	defer logger.Close()

	// Log at different levels
	logger.Debug("This is a debug message")
	logger.Info("Application started successfully")
	logger.Infof("Processing %d items", 100)
	logger.Warn("This is a warning message")
	logger.Error("This is an error message")

	// Generate some logs to demonstrate rotation
	for i := 0; i < 1000; i++ {
		logger.Infof("Log entry #%d - generating content to trigger rotation", i)
		if i%100 == 0 {
			time.Sleep(10 * time.Millisecond) // Small delay
		}
	}

	// Manually rotate the log
	logger.Info("Manually rotating log file...")
	if err := logger.Rotate(); err != nil {
		logger.Errorf("Failed to rotate log: %v", err)
	} else {
		logger.Info("Log rotation completed successfully")
	}

	logger.Info("Example completed - check ./example_logs/ directory for log files")
}
