package utils

import (
	"os"

	"go.uber.org/zap"
	"go.uber.org/zap/zapcore"
)

// <PERSON><PERSON> wraps zap logger with convenience methods
type Logger struct {
    *zap.SugaredLogger
}

// NewLogger creates a new logger instance
func NewLogger(name string) *Logger {
    // Configure logger
    config := zap.NewProductionConfig()
    config.EncoderConfig.TimeKey = "time"
    config.EncoderConfig.EncodeTime = zapcore.ISO8601TimeEncoder
    
    // Set log level from environment
    logLevel := os.Getenv("LOG_LEVEL")
    switch logLevel {
    case "debug":
        config.Level = zap.NewAtomicLevelAt(zapcore.DebugLevel)
    case "info":
        config.Level = zap.NewAtomicLevelAt(zapcore.InfoLevel)
    case "warn":
        config.Level = zap.NewAtomicLevelAt(zapcore.WarnLevel)
    case "error":
        config.Level = zap.NewAtomicLevelAt(zapcore.ErrorLevel)
    default:
        config.Level = zap.NewAtomicLevelAt(zapcore.InfoLevel)
    }

    logger, err := config.Build(zap.AddCallerSkip(1))
    if err != nil {
        panic(err)
    }

    sugar := logger.Sugar().With("component", name)
    return &Logger{sugar}
}