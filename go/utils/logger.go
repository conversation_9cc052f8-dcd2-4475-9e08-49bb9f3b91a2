package utils

import (
	"fmt"
	"os"
	"path/filepath"
	"time"

	"go.uber.org/zap"
	"go.uber.org/zap/zapcore"
)

// <PERSON>gger wraps zap logger with convenience methods
type Logger struct {
    *zap.SugaredLogger
    file *os.File
}

// NewLogger creates a new logger instance that writes to both console and file
func NewLogger(name string) *Logger {
    // Create logs directory if it doesn't exist
    logDir := getEnvOrDefault("LOG_DIR", "logs")
    if err := os.MkdirAll(logDir, 0755); err != nil {
        panic(fmt.Sprintf("Failed to create log directory: %v", err))
    }

    // Create log file with timestamp
    timestamp := time.Now().Format("2006-01-02")
    logFile := filepath.Join(logDir, fmt.Sprintf("trading-bot-%s.log", timestamp))

    // Open log file
    file, err := os.OpenFile(logFile, os.O_CREATE|os.O_WRONLY|os.O_APPEND, 0666)
    if err != nil {
        panic(fmt.Sprintf("Failed to open log file: %v", err))
    }

    // Set log level from environment
    logLevel := getLogLevel()

    // Create encoder config
    encoderConfig := zapcore.EncoderConfig{
        TimeKey:        "time",
        LevelKey:       "level",
        NameKey:        "logger",
        CallerKey:      "caller",
        MessageKey:     "msg",
        StacktraceKey:  "stacktrace",
        LineEnding:     zapcore.DefaultLineEnding,
        EncodeLevel:    zapcore.LowercaseLevelEncoder,
        EncodeTime:     zapcore.ISO8601TimeEncoder,
        EncodeDuration: zapcore.SecondsDurationEncoder,
        EncodeCaller:   zapcore.ShortCallerEncoder,
    }

    // Create console encoder (more human-readable)
    consoleEncoder := zapcore.NewConsoleEncoder(encoderConfig)

    // Create file encoder (JSON format for structured logging)
    fileEncoder := zapcore.NewJSONEncoder(encoderConfig)

    // Create cores for console and file
    consoleCore := zapcore.NewCore(
        consoleEncoder,
        zapcore.AddSync(os.Stdout),
        logLevel,
    )

    fileCore := zapcore.NewCore(
        fileEncoder,
        zapcore.AddSync(file),
        logLevel,
    )

    // Combine cores
    core := zapcore.NewTee(consoleCore, fileCore)

    // Create logger with caller skip
    logger := zap.New(core, zap.AddCaller(), zap.AddCallerSkip(1))

    sugar := logger.Sugar().With("component", name)
    return &Logger{
        SugaredLogger: sugar,
        file:          file,
    }
}

// Close closes the log file and syncs any buffered logs
func (l *Logger) Close() error {
    // Sync the logger to flush any buffered logs
    if err := l.SugaredLogger.Sync(); err != nil {
        // Ignore sync errors on stdout/stderr as they're expected on some systems
        if l.file != nil {
            l.file.Close()
        }
        return err
    }

    // Close the file
    if l.file != nil {
        return l.file.Close()
    }

    return nil
}

// Sync flushes any buffered log entries
func (l *Logger) Sync() error {
    return l.SugaredLogger.Sync()
}

// getLogLevel returns the log level from environment variable
func getLogLevel() zapcore.Level {
    logLevel := os.Getenv("LOG_LEVEL")
    switch logLevel {
    case "debug":
        return zapcore.DebugLevel
    case "info":
        return zapcore.InfoLevel
    case "warn":
        return zapcore.WarnLevel
    case "error":
        return zapcore.ErrorLevel
    default:
        return zapcore.InfoLevel
    }
}

