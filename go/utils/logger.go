package utils

import (
	"fmt"
	"io"
	"os"
	"path/filepath"
	"strconv"

	"go.uber.org/zap"
	"go.uber.org/zap/zapcore"
	"gopkg.in/natefinch/lumberjack.v2"
)

// Logger wraps zap logger with convenience methods
type Logger struct {
    *zap.SugaredLogger
    rotatingWriter io.WriteCloser
}

// NewLogger creates a new logger instance that writes to both console and file with rotation
func NewLogger(name string) *Logger {
    // Create logs directory if it doesn't exist
    logDir := getEnvOrDefault("LOG_DIR", "logs")
    if err := os.MkdirAll(logDir, 0755); err != nil {
        panic(fmt.Sprintf("Failed to create log directory: %v", err))
    }

    // Create rotating log file writer using lumberjack
    logFile := filepath.Join(logDir, "trading-bot.log")

    // Configure log rotation parameters from environment variables
    maxSize := getEnvAsInt("LOG_MAX_SIZE", 100)        // Max size in MB before rotation
    maxBackups := getEnvAsInt("LOG_MAX_BACKUPS", 5)    // Max number of old log files to keep
    maxAge := getEnvAsInt("LOG_MAX_AGE", 30)           // Max age in days to keep old log files
    compress := getEnvAsBool("LOG_COMPRESS", true)     // Whether to compress old log files

    rotatingWriter := &lumberjack.Logger{
        Filename:   logFile,
        MaxSize:    maxSize,
        MaxBackups: maxBackups,
        MaxAge:     maxAge,
        Compress:   compress,
        LocalTime:  true, // Use local time for log file timestamps
    }

    // Set log level from environment
    logLevel := getLogLevel()

    // Create encoder config
    encoderConfig := zapcore.EncoderConfig{
        TimeKey:        "time",
        LevelKey:       "level",
        NameKey:        "logger",
        CallerKey:      "caller",
        MessageKey:     "msg",
        StacktraceKey:  "stacktrace",
        LineEnding:     zapcore.DefaultLineEnding,
        EncodeLevel:    zapcore.LowercaseLevelEncoder,
        EncodeTime:     zapcore.ISO8601TimeEncoder,
        EncodeDuration: zapcore.SecondsDurationEncoder,
        EncodeCaller:   zapcore.ShortCallerEncoder,
    }

    // Create console encoder (more human-readable)
    consoleEncoder := zapcore.NewConsoleEncoder(encoderConfig)

    // Create file encoder (JSON format for structured logging)
    fileEncoder := zapcore.NewJSONEncoder(encoderConfig)

    // Create cores for console and file
    consoleCore := zapcore.NewCore(
        consoleEncoder,
        zapcore.AddSync(os.Stdout),
        logLevel,
    )

    fileCore := zapcore.NewCore(
        fileEncoder,
        zapcore.AddSync(rotatingWriter),
        logLevel,
    )

    // Combine cores
    core := zapcore.NewTee(consoleCore, fileCore)

    // Create logger with caller skip
    logger := zap.New(core, zap.AddCaller(), zap.AddCallerSkip(1))

    sugar := logger.Sugar().With("component", name)
    return &Logger{
        SugaredLogger:  sugar,
        rotatingWriter: rotatingWriter,
    }
}

// Close closes the log file and syncs any buffered logs
func (l *Logger) Close() error {
    // Sync the logger to flush any buffered logs
    if err := l.SugaredLogger.Sync(); err != nil {
        // Ignore sync errors on stdout/stderr as they're expected on some systems
        if l.rotatingWriter != nil {
            l.rotatingWriter.Close()
        }
        return err
    }

    // Close the rotating writer
    if l.rotatingWriter != nil {
        return l.rotatingWriter.Close()
    }

    return nil
}

// Sync flushes any buffered log entries
func (l *Logger) Sync() error {
    return l.SugaredLogger.Sync()
}

// Rotate manually rotates the log file (useful for log rotation on demand)
func (l *Logger) Rotate() error {
    if rotator, ok := l.rotatingWriter.(*lumberjack.Logger); ok {
        return rotator.Rotate()
    }
    return nil
}

// getLogLevel returns the log level from environment variable
func getLogLevel() zapcore.Level {
    logLevel := os.Getenv("LOG_LEVEL")
    switch logLevel {
    case "debug":
        return zapcore.DebugLevel
    case "info":
        return zapcore.InfoLevel
    case "warn":
        return zapcore.WarnLevel
    case "error":
        return zapcore.ErrorLevel
    default:
        return zapcore.InfoLevel
    }
}

// getEnvAsInt returns environment variable as integer with default value
func getEnvAsInt(key string, defaultValue int) int {
    if value := os.Getenv(key); value != "" {
        if intValue, err := strconv.Atoi(value); err == nil {
            return intValue
        }
    }
    return defaultValue
}

// getEnvAsBool returns environment variable as boolean with default value
func getEnvAsBool(key string, defaultValue bool) bool {
    if value := os.Getenv(key); value != "" {
        if boolValue, err := strconv.ParseBool(value); err == nil {
            return boolValue
        }
    }
    return defaultValue
}

