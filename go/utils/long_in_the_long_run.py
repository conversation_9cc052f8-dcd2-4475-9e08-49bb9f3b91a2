import os
from decimal import Decimal  # , getcontext
from statistics import mean
from hummingbot.core.data_type.common import OrderType
from hummingbot.strategy.script_strategy_base import ScriptStrategyBase
from hummingbot.connector.utils import split_hb_trading_pair
from typing import List
from hummingbot.core.data_type.order_candidate import OrderCandidate
from hummingbot.connector.exchange_base import ExchangeBase
from hummingbot.core.event.events import OrderFilledEvent, OrderType, TradeType, BuyOrderCreatedEvent, SellOrderCreatedEvent, OrderCancelledEvent, MarketOrderFailureEvent
import requests
from scripts.pg_trading import PgTrading, ORDER_STATUS_BUYING, ORDER_STATUS_BOUGHT, ORDER_STATUS_SELLING
import pandas as pd

# 用这个会有问题，要注释掉
# getcontext().prec = 8


class LongInTheLongRun(ScriptStrategyBase):
    """
    insert into orders(id, buy_order_no, sell_order_no, pair, status, buy_price, buy_amount, buy_amount_filled, buy_start_at, buy_end_at, sell_price, sell_amount_filled, sell_start_at) 
values(2, 'x-MG43PCSNBBCUT629fe4b9b8fc8c2aa', 'x-MG43PCSNSBCUT629ff47228c22ce03', 'BTC-USDT', 'SELLING', '94173.8847800000000000', '0.0095500000000000', '0.0095500000000000', '2024-12-24 06:33:47.182151+00', '2024-12-24 07:11:16.004275+00', '95147.0625240000000000', '0.0000000000000000', '2024-12-24 07:44:07.109744+00')
    
    1. 在价格下降时买入BTC，第一次用MA, 在有未卖出持仓时，当前价格跟第一次价格做买入对比
    2. 等有利润了卖出, 第一次买入等有了利润就卖出，后面的买入用前一次的买入价做卖出对比
    3. 如果买入单太长时间没有利润，可以手动设置成manual好重新再来

    这里有order book的样例
    /home/<USER>/Documents/code/hummingbot/scripts/utility/download_order_book_and_trades.py

    这里有另外一个样例
    /home/<USER>/Documents/code/hummingbot/scripts/community/buy_dip_example.py
    
    # telegram bot notification
    self.notify_hb_app(msg)
    self.notify_hb_app_with_timestamp(msg)
    https://hummingbot.org/global-configs/telegram/
    
    # add new command
    https://hummingbot.org/client/commands-shortcuts/#adding-new-commands
    """
    connector_name = os.getenv("TRADING_EXCHANGE", "binance_paper_trade")
    trading_pair = "BTC-USDT"

    # 先写在这里，第一阶段先实现BTC,
    # pair_eth_usdt = "ETH-USDT"
    # pair_eth_btc = "ETH-BTC"
    markets = {
        connector_name: {trading_pair
                         # , pair_eth_usdt
                         # , pair_eth_btc
                         },
    }

    # base_asset, quote_asset = split_hb_trading_pair(pair_btc_usdt)
    # conversion_pair: str = f"{quote_asset}-USD"
    buy_usd_amount: Decimal = Decimal("936")
    dip_percentages: List[Decimal] = [
        Decimal("0.004"),
        Decimal("0.004"),
        Decimal("0.004"),
        Decimal("0.004"),
        Decimal("0.004"),
        Decimal("0.004"),
        Decimal("0.004"),
        Decimal("0.004"),
        Decimal("0.004"),
        Decimal("0.004"),
        Decimal("0.004"),
        Decimal("0.004"),
        Decimal("0.004"),
        Decimal("0.004"),
        Decimal("0.004"),
        Decimal("0.004"),
        Decimal("0.004"),
        Decimal("0.004"),
    ]

    pg_trading = PgTrading()
    fatal_msg = ""
    active_orders_syned = False

    @property
    def connector(self) -> ExchangeBase:
        return self.connectors[self.connector_name]

    def round_down_5(self, num):
        return num.quantize(Decimal('1.00000'), rounding='ROUND_DOWN')

    def round_down_2(self, num):
        return num.quantize(Decimal('1.00'), rounding='ROUND_DOWN')

    def on_tick(self):
        if len(self.fatal_msg) > 0:
            return
        # try:
        if not self.active_orders_syned:
            self.syn_active_orders()
            self.active_orders_syned = True
            return
        self.create_or_replace_buying_order()
        # except Exception as e:
        #    msg = 'on_tick Error:' + repr(e)
        #    self.logger().warning(msg)
        #    self.notify_hb_app(msg)
        #    self.fatal_msg = msg

    def cal_buy_order_price_by_avg_close(self, avg_close, current_price, quiet=False):
        order_price = self.round_down_2(
            avg_close * (Decimal("1") - self.dip_percentages[0]))
        if not quiet:
            self.logger().info(
                f"Avg_close_before:{avg_close} current_price:{current_price}")
        if order_price > current_price:
            if not quiet:
                self.logger().info(
                    f"(avg_close * (Decimal(1) - self.dip_percentages[0])) > current_price. {order_price}>{current_price}. fix order_price!")
            order_price = self.round_down_2(current_price * Decimal("0.998"))
        return order_price

    def cal_buy_order_price_last_bought(self, len_pending_orders, last_bought_price):
        order_price = self.round_down_2(
            last_bought_price * (Decimal("1") - self.dip_percentages[len_pending_orders]))
        self.logger().info(
            f"Cal order price base on last bought, len_pending_orders:{len_pending_orders}, last_bought_price:{last_bought_price} order_price:{order_price}")
        return order_price

    def create_or_replace_buying_order(self) -> List[OrderCandidate]:
        pending_orders = self.pg_trading.get_pending_orders()
        if len(pending_orders) > len(self.dip_percentages):
            raise Exception(
                f"len(pending_orders):{len(pending_orders)} > len(dip_percentages):{len(self.dip_percentages)}.")

        avg_close = self._get_avg_close_before(self.trading_pair)
        current_price = self.connector.get_price(self.trading_pair, True)

        if len(pending_orders) > 0:
            order0 = pending_orders[0]
            # 如果完全没有成交，那尝试看看需不需要取消重新下单
            # 只有len(pending_orders)==1，那这单才是按照市场价来买到的
            if order0.status == ORDER_STATUS_BUYING and len(pending_orders) == 1:
                order_price = self.cal_buy_order_price_by_avg_close(
                    avg_close, current_price, True)
                if order_price > self.round_down_2(order0.buy_price * Decimal("1.002")):
                    self.logger().info(
                        f"Replace buying order, order0.buy_order_no:{order0.buy_order_no} order0.buy_price:{order0.buy_price} new order_price:{order_price}")
                    # 这次只需要cancel，下次on_tick的时候会创建新的订单
                    self.cancel(self.connector_name,
                                self.trading_pair, order0.buy_order_no)
                    # 取消我们直接设置成BOUGHT, 因为有可能已经部分成交了, 等待下次处理
                    self.pg_trading.update_buy_amount_filled(
                        order0.buy_order_no, order0.buy_amount_filled, True)
            elif order0.status == ORDER_STATUS_BOUGHT:  # 已经是BOUGHT状态，但是不一定有成交
                if order0.buy_amount_filled == Decimal("0"):
                    # 实际上一点没成交，不需要发卖单
                    self.logger().info(
                        f"Terminate order that donesn't has any buy amount filled. buy_order_no:{order0.buy_order_no}")
                    self.pg_trading.order_start_sell(
                        order0.buy_order_no, "cancel-" + order0.buy_order_no, Decimal("0"))
                    self.pg_trading.update_sell_amount_filled(
                        "cancel-" + order0.buy_order_no, Decimal("0"), True)
                else:
                    order_price = order0.buy_price * \
                        (Decimal("1") +
                         self.dip_percentages[len(pending_orders)]) + order0.buy_price * Decimal("0.001") * Decimal(len(pending_orders) - 1)
                    order_price = self.round_down_2(order_price)
                    self.logger().info(
                        f"Start to sell BOUGHT order, buy_order_no:{order0.buy_order_no} buy_amount_filled:{order0.buy_amount_filled} sell_price:{order_price}")
                    self.place_sell_order(
                        order0.buy_order_no, order_price, order0.buy_amount_filled)
            elif order0.status == ORDER_STATUS_SELLING:
                order_price = self.cal_buy_order_price_last_bought(
                    len(pending_orders), order0.buy_price)
                self.logger().info(
                    f"Create new buy order base on last bought, order_price:{order_price}, current_price:{current_price}")
                self.place_buy_order(order_price)
            else:
                pass  # just wait
        else:
            order_price = self.cal_buy_order_price_by_avg_close(
                avg_close, current_price)
            self.logger().info(
                f"Create new buy order base on avg, order_price:{order_price}, current_price:{current_price}")
            self.place_buy_order(order_price)

    def syn_active_orders(self):
        self.logger().info("calling syn_active_orders...")
        # 重启后脚本会自动取消交易所的订单
        pg_orders = self.pg_trading.get_pending_orders()
        connector_orders = self.get_active_orders(self.connector_name)
        for pg_order in pg_orders:
            if pg_order.status == ORDER_STATUS_BOUGHT:
                continue
            not_in_exchange = True
            for connector_order in connector_orders:
                if connector_order.client_order_id == pg_order.buy_order_no or connector_order.client_order_id == pg_order.sell_order_no:
                    not_in_exchange = False
                    break
            if not_in_exchange:
                if pg_order.status == ORDER_STATUS_BUYING:
                    # 这里也不可能重新发单，因为重新发单数据的订单id是不一样的
                    self.logger().info(
                        f"Buying order not in exchange, buy_order_no:{pg_order.buy_order_no}. update status to:{ORDER_STATUS_BOUGHT}")
                    self.pg_trading.update_buy_amount_filled(
                        pg_order.buy_order_no, pg_order.buy_amount_filled, True)
                elif pg_order.status == ORDER_STATUS_SELLING:
                    if pg_order.buy_amount_filled - pg_order.sell_amount_filled > 0:
                        self.logger().info(f"Selling order not in exchange, buy_order_no:{pg_order.buy_order_no}. sell_order_no:{pg_order.sell_order_no}. " +
                                           f"Recreate selling order, sell_price:{pg_order.sell_price}, amount:{pg_order.buy_amount_filled - pg_order.sell_amount_filled}")
                        self.place_sell_order(pg_order.buy_order_no, pg_order.sell_price,
                                              pg_order.buy_amount_filled - pg_order.sell_amount_filled)
        self.logger().info("syn_active_orders done.")

    def place_buy_order(self, order_price):
        proposal = []
        amount = self.round_down_5((self.buy_usd_amount / order_price))
        candidate = OrderCandidate(self.trading_pair, True, OrderType.LIMIT_MAKER, TradeType.BUY, amount,
                                   order_price)
        proposal.append(candidate)
        proposal = self.connector.budget_checker.adjust_candidates(
            proposal, all_or_none=False)
        if proposal:
            self.logger().info("place_buy_order enter proposal")
            for candidate in proposal:
                amount_updated = self.round_down_5(candidate.amount)
                self.logger().info(
                    f"place_buy_order enter candidate candidate.amount:{amount_updated}")
                if amount_updated > Decimal("0"):
                    buy_order_no = self.buy(self.connector_name, self.trading_pair, amount_updated, candidate.order_type,
                                            candidate.price)
                    self.pg_trading.order_start_buy(
                        buy_order_no, self.trading_pair, candidate.price, amount_updated)
                else:
                    msg = "budget_checker return zero amount"
                    self.logger().warning(msg)
                    self.notify_hb_app(msg)

    def place_sell_order(self, buy_order_no, order_price, amount):
        self.logger().info(f"place_sell_order amount:{amount}")
        if amount <= Decimal("0"):
            return
        sell_order_no = self.sell(self.connector_name, self.trading_pair, amount, OrderType.LIMIT_MAKER,
                                  order_price)
        self.pg_trading.order_start_sell(
            buy_order_no, sell_order_no, order_price)

    def _get_avg_close_before(self, trading_pair: str) -> Decimal:
        """
        Fetches binance candle stick data and returns a list daily close
        This is the API response data structure:
        [
          [
            1499040000000,      // Open time
            "0.01634790",       // Open
            "0.80000000",       // High
            "0.01575800",       // Low
            "0.01577100",       // Close
            "148976.11427815",  // Volume
            1499644799999,      // Close time
            "2434.19055334",    // Quote asset volume
            308,                // Number of trades
            "1756.87402397",    // Taker buy base asset volume
            "28.46694368",      // Taker buy quote asset volume
            "17928899.62484339" // Ignore.
          ]
        ]

        :param trading_pair: A market trading pair to

        :return: A list of daily close
        """

        url = "https://api.binance.com/api/v3/klines"
        params = {"symbol": trading_pair.replace("-", ""),
                  "interval": "3m",
                  "limit": 40  # 2 h
                  }
        records = requests.get(url=url, params=params).json()
        closes = [Decimal(str(record[4]))
                  for record in records[:30]]  # 只取前的半小时为平均值
        m = mean(closes)
        return self.round_down_2(m)

    def did_fill_order(self, event: OrderFilledEvent):
        msg = (f"({event.trading_pair}) {event.trade_type.name} order (price: {event.price}) of {event.amount} "
               f"{split_hb_trading_pair(event.trading_pair)[0]} is filled.")
        self.logger().info(msg)
        self.notify_hb_app(msg)
        order = self.pg_trading.get_order_by_order_no(event.order_id)
        if order is not None:  # buy
            buy_amount_filled = order.buy_amount_filled + event.amount
            self.pg_trading.update_buy_amount_filled(
                event.order_id, buy_amount_filled, buy_amount_filled >= order.buy_amount)
        else:
            # try to find by sell order no
            order = self.pg_trading.get_order_by_order_no("", event.order_id)
            if order is None:
                self.logger().warning(
                    f"Can not find order_no:{event.order_id} in pg when did_fill_order")
            sell_amount_filled = order.sell_amount_filled + event.amount
            self.pg_trading.update_sell_amount_filled(
                event.order_id, sell_amount_filled, sell_amount_filled >= order.buy_amount_filled)
            # cancel buying order so that next tick it can create a new one with updated price
            pending_orders = self.pg_trading.get_pending_orders()
            if len(pending_orders) > 0:
                order0 = pending_orders[0]
                if order0.status == ORDER_STATUS_BUYING:
                    # 这次只需要cancel，下次on_tick的时候会创建新的订单
                    self.cancel(self.connector_name,
                                self.trading_pair, order0.buy_order_no)
                    # 取消我们直接设置成BOUGHT, 因为有可能已经部分成交了, 等待下次处理
                    self.pg_trading.update_buy_amount_filled(
                        order0.buy_order_no, order0.buy_amount_filled, True)

    def did_create_buy_order(self, event: BuyOrderCreatedEvent):
        self.logger().info(
            f"Buy order created. order_no:{event.order_id} price:{event.price} amount:{event.amount}")

    def did_create_sell_order(self, event: SellOrderCreatedEvent):
        self.logger().info(
            f"Sell order created. order_no:{event.order_id} price:{event.price} amount:{event.amount}")

    def did_cancel_order(self, event: OrderCancelledEvent):
        self.logger().info(f"Order canceled. order_no:{event.order_id}")

    def did_fail_order(self, event: MarketOrderFailureEvent):
        msg = f"Order failed. order_no: {event.order_id}"
        self.logger().warning(msg)
        self.notify_hb_app(msg)

    def active_orders_df(self) -> pd.DataFrame:
        """
        Return a data frame of all active orders for displaying purpose.
        """
        columns = ["Exchange", "Market", "ID",
                   "Side", "Price", "Amount", "Age"]
        data = []
        for connector_name, connector in self.connectors.items():
            for order in self.get_active_orders(connector_name):
                age_txt = "n/a" if order.age() <= 0. else pd.Timestamp(order.age(),
                                                                       unit='s').strftime('%H:%M:%S')
                data.append([
                    connector_name,
                    order.trading_pair,
                    order.client_order_id,
                    "buy" if order.is_buy else "sell",
                    float(order.price),
                    float(order.quantity),
                    age_txt
                ])
        if not data:
            raise ValueError
        df = pd.DataFrame(data=data, columns=columns)
        df.sort_values(by=["Exchange", "Market", "Side"], inplace=True)
        return df

    def pg_pending_order_df(self) -> pd.DataFrame:
        columns = ["id", "buy_start_at", "buy_order_no", "sell_order_no", "status",
                   "buy_price", "buy_amount", "buy_amount_filled", "sell_price", "sell_amount_filled"]
        data = []
        for order in self.pg_trading.get_pending_orders():
            data.append([
                order.id,
                order.buy_start_at,
                order.buy_order_no,
                order.sell_order_no,
                order.status,
                order.buy_price,
                order.buy_amount,
                order.buy_amount_filled,
                order.sell_price,
                order.sell_amount_filled
            ])
        if not data:
            raise ValueError
        df = pd.DataFrame(data=data, columns=columns)
        return df

    def format_status(self) -> str:
        """
        Returns status of the current strategy on user balances and current active orders. This function is called
        when status command is issued. Override this function to create custom status display output.
        """
        if not self.ready_to_trade:
            return "Market connectors are not ready."
        lines = []
        warning_lines = []
        warning_lines.extend(self.network_warning(
            self.get_market_trading_pair_tuples()))

        balance_df = self.get_balance_df()
        lines.extend(["", "  Balances:"] + ["    " +
                     line for line in balance_df.to_string(index=False).split("\n")])

        try:
            df = self.active_orders_df()
            lines.extend(["", "  Orders:"] + ["    " +
                         line for line in df.to_string(index=False).split("\n")])
        except ValueError:
            lines.extend(["", "  No active maker orders."])

        try:
            df = self.pg_pending_order_df()
            lines.extend(["", "  PgPendingOrders:"])
            for index, row in df.iterrows():
                for column, value in row.items():
                    lines.extend([f"    {column}: {value}"])
                lines.extend([f"--------------------"])
        except ValueError:
            lines.extend(["", "  No pg pending orders."])

        warning_lines.extend(self.balance_warning(
            self.get_market_trading_pair_tuples()))
        if len(warning_lines) > 0:
            lines.extend(["", "*** WARNINGS ***"] + warning_lines)
        return "\n".join(lines)
