package utils

import (
	"bytes"
	"encoding/json"
	"fmt"
	"net/http"
	"os"
	"time"
)

// TelegramClient handles sending messages to Telegram
type TelegramClient struct {
	botToken  string
	chatID    string
	logger    *Logger
	client    *http.Client
	isEnabled bool
}

// NewTelegramClient creates a new TelegramClient
func NewTelegramClient() *TelegramClient {
	logger := NewLogger("telegram")
	botToken := os.Getenv("TELEGRAM_BOT_TOKEN")
	chatID := os.Getenv("TELEGRAM_CHAT_ID")
	isEnabled := botToken != "" && chatID != ""

	if !isEnabled {
		logger.Info("Telegram notifications disabled: missing TELEGRAM_BOT_TOKEN or TELEGRAM_CHAT_ID")
	}

	return &TelegramClient{
		botToken:  botToken,
		chatID:    chatID,
		logger:    logger,
		client:    &http.Client{Timeout: 10 * time.Second},
		isEnabled: isEnabled,
	}
}

// SendMessage sends a message to the Telegram chat
func (t *TelegramClient) SendMessage(message string) error {
	if !t.isEnabled {
		t.logger.Infof("Telegram message (not sent): %s", message)
		return nil
	}

	url := fmt.Sprintf("https://api.telegram.org/bot%s/sendMessage", t.botToken)
	
	// Prepare request body
	body := map[string]interface{}{
		"chat_id":    t.chatID,
		"text":       message,
		"parse_mode": "HTML",
	}
	
	jsonBody, err := json.Marshal(body)
	if err != nil {
		return fmt.Errorf("failed to marshal request body: %w", err)
	}
	
	// Send request
	resp, err := t.client.Post(url, "application/json", bytes.NewBuffer(jsonBody))
	if err != nil {
		return fmt.Errorf("failed to send Telegram message: %w", err)
	}
	defer resp.Body.Close()
	
	// Check response
	if resp.StatusCode != http.StatusOK {
		return fmt.Errorf("Telegram API returned non-OK status: %d", resp.StatusCode)
	}
	
	t.logger.Infof("Telegram message sent: %s", message)
	return nil
}
