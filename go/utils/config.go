package utils

import (
	"os"
	"strconv"
	"strings"
	"time"
)

// Config holds application configuration loaded from environment variables
type Config struct {
	// Exchange settings
	ExchangeAPIKey    string
	ExchangeSecretKey string

	// Telegram settings
	TelegramToken        string
	TelegramAllowedUsers []int

	// Database settings
	DBHost     string
	DBPort     string
	DBName     string
	DBUser     string
	DBPassword string

	// General settings
	TradingMode string // 'live' or 'backtest'

	// Backtest settings
	BacktestStartTime      time.Time
	BacktestEndTime        time.Time
	BacktestInitialBalance map[string]float64
	BacktestTradingFee     float64
	BacktestTimeStep       int // in seconds

	// Strategy settings
	StrategyName string
}

// NewConfig creates a new Config instance with values from environment variables
func NewConfig() *Config {
	// Parse telegram allowed users
	telegramAllowedUsers := []int{}
	telegramAllowedUsersStr := getEnvOrDefault("TELEGRAM_ALLOWED_USERS", "")
	if telegramAllowedUsersStr != "" {
		for _, idStr := range strings.Split(telegramAllowedUsersStr, ",") {
			if idStr == "" {
				continue
			}
			id, err := strconv.Atoi(idStr)
			if err == nil {
				telegramAllowedUsers = append(telegramAllowedUsers, id)
			}
		}
	}

	// Parse backtest initial balance
	backtestInitialBalance := make(map[string]float64)
	backtestInitialETH, _ := strconv.ParseFloat(getEnvOrDefault("BACKTEST_INITIAL_ETH", "0"), 64)
	backtestInitialBTC, _ := strconv.ParseFloat(getEnvOrDefault("BACKTEST_INITIAL_BTC", "0"), 64)
	backtestInitialBalance["ETH"] = backtestInitialETH
	backtestInitialBalance["BTC"] = backtestInitialBTC

	// Parse backtest trading fee
	backtestTradingFee, _ := strconv.ParseFloat(getEnvOrDefault("BACKTEST_TRADING_FEE", "0.001"), 64)

	// Parse backtest time step
	backtestTimeStep, _ := strconv.Atoi(getEnvOrDefault("BACKTEST_TIME_STEP", "60"))

	// Parse backtest start and end times
	backtestStartTimeStr := getEnvOrDefault("BACKTEST_START_TIME", "2023-01-01T00:00:00+08:00")
	backtestEndTimeStr := getEnvOrDefault("BACKTEST_END_TIME", "2023-01-31T23:59:59+08:00")

	backtestStartTime, err := time.Parse(time.RFC3339, backtestStartTimeStr)
	if err != nil {
		panic(err)
	}
	backtestEndTime, err := time.Parse(time.RFC3339, backtestEndTimeStr)
	if err != nil {
		panic(err)
	}

	return &Config{
		// Exchange settings
		ExchangeAPIKey:    getEnvOrDefault("EXCHANGE_API_KEY", ""),
		ExchangeSecretKey: getEnvOrDefault("EXCHANGE_SECRET_KEY", ""),

		// Telegram settings
		TelegramToken:        getEnvOrDefault("TELEGRAM_TOKEN", ""),
		TelegramAllowedUsers: telegramAllowedUsers,

		// Database settings
		DBHost:     getEnvOrDefault("DB_HOST", "localhost"),
		DBPort:     getEnvOrDefault("DB_PORT", "5432"),
		DBName:     getEnvOrDefault("DB_NAME", "trading"),
		DBUser:     getEnvOrDefault("DB_USER", "trading"),
		DBPassword: getEnvOrDefault("DB_PASSWORD", "123456"),

		// General settings
		TradingMode: getEnvOrDefault("TRADING_MODE", "backtest"),

		// Backtest settings
		BacktestStartTime:      backtestStartTime,
		BacktestEndTime:        backtestEndTime,
		BacktestInitialBalance: backtestInitialBalance,
		BacktestTradingFee:     backtestTradingFee,
		BacktestTimeStep:       backtestTimeStep,

		// Strategy settings
		StrategyName: getEnvOrDefault("STRATEGY_NAME", "react_to_market_cap"),
	}
}

// getEnvOrDefault returns the value of an environment variable or a default value
func getEnvOrDefault(key, defaultValue string) string {
	if value, exists := os.LookupEnv(key); exists {
		return value
	}
	return defaultValue
}
