package utils

import (
	"github.com/shopspring/decimal"
)

// TechnicalIndicators provides utility functions for calculating technical indicators
type TechnicalIndicators struct{}

// GetCurrentEMA calculates the Exponential Moving Average for the given prices
func (t *TechnicalIndicators) GetCurrentEMA(prices []decimal.Decimal, period int) decimal.Decimal {
	if len(prices) < period {
		return decimal.Zero
	}

	// Calculate multiplier: 2 / (period + 1)
	multiplier := decimal.NewFromInt(2).Div(decimal.NewFromInt(int64(period + 1)))
	
	// Calculate SMA for initial EMA
	sma := decimal.Zero
	for i := 0; i < period; i++ {
		sma = sma.Add(prices[i])
	}
	sma = sma.Div(decimal.NewFromInt(int64(period)))
	
	// Calculate EMA
	ema := sma
	for i := period; i < len(prices); i++ {
		ema = prices[i].Mul(multiplier).Add(ema.Mul(decimal.NewFromInt(1).Sub(multiplier)))
	}
	
	return ema
}

// NewTechnicalIndicators creates a new TechnicalIndicators instance
func NewTechnicalIndicators() *TechnicalIndicators {
	return &TechnicalIndicators{}
}
