package strategies

import (
	"encoding/json"
	"os"

	"github.com/shopspring/decimal"
)

// MovingAverageCrossConfig represents configuration for a moving average crossover
type MovingAverageCrossConfig struct {
	LongTerm  int
	ShortTerm int
}

// ReactToMarketCapConfig holds configuration for the ReactToMarketCapStrategy
type ReactToMarketCapConfig struct {
	TradingCoins       map[string][]MovingAverageCrossConfig
	InitialCoin        string
	CoingeckoAPIURL    string
	EMALongInterval    string
	EMAShortInterval   string
	DeviationThreshold decimal.Decimal
}

// NewReactToMarketCapConfig creates a new configuration with default values
func NewReactToMarketCapConfig() *ReactToMarketCapConfig {
	return &ReactToMarketCapConfig{
		TradingCoins:       tradingPairsFromEnvironment(),
		InitialCoin:        getEnvOrDefault("INITIAL_COIN", "BTC"),
		CoingeckoAPIURL:    "https://api.coingecko.com/api/v3/coins/markets",
		EMALongInterval:    getEnvOrDefault("EMA_LONG_INTERVAL", "1h"),
		EMAShortInterval:   getEnvOrDefault("EMA_SHORT_INTERVAL", "5m"),
		DeviationThreshold: decimal.RequireFromString(getEnvOrDefault("DEVIATION_THRESHOLD", "0.001")),
	}
}

// tradingPairsFromEnvironment loads trading pairs configuration from environment variables
func tradingPairsFromEnvironment() map[string][]MovingAverageCrossConfig {
	jsonStr := getEnvOrDefault("TRADING_COINS", "{}")
	
	var data map[string][]map[string]interface{}
	if err := json.Unmarshal([]byte(jsonStr), &data); err != nil {
		return make(map[string][]MovingAverageCrossConfig)
	}
	
	result := make(map[string][]MovingAverageCrossConfig)
	for pair, configs := range data {
		var maConfigs []MovingAverageCrossConfig
		for _, config := range configs {
			longTerm, _ := config["long_term"].(float64)
			shortTerm, _ := config["short_term"].(float64)
			
			maConfigs = append(maConfigs, MovingAverageCrossConfig{
				LongTerm:  int(longTerm),
				ShortTerm: int(shortTerm),
			})
		}
		result[pair] = maConfigs
	}
	
	return result
}

// getEnvOrDefault returns the value of an environment variable or a default value
func getEnvOrDefault(key, defaultValue string) string {
	if value, exists := os.LookupEnv(key); exists {
		return value
	}
	return defaultValue
}
