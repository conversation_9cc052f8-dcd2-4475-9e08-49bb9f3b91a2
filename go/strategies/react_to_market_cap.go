package strategies

import (
	"context"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"strings"
	"sync"
	"time"

	"github.com/kenny/trading-bot/go/db"
	"github.com/kenny/trading-bot/go/exchange"
	"github.com/kenny/trading-bot/go/utils"
	"github.com/shopspring/decimal"
)

// TradingDB interface defines the database operations needed by the strategy
type TradingDB interface {
	ReactToMarketCapGetAllCurrentBalances(ctx context.Context) ([]db.ReactToMarketCapBalance, error)
	ReactToMarketCapUpdateCurrentBalance(ctx context.Context, pair string, longTerm, shortTerm int, amount decimal.Decimal) (int64, error)
	ReactToMarketCapCurrentBalanceOrderPlaced(ctx context.Context, id int64, orderID string) error
	ReactToMarketCapCurrentBalanceOrderCompleted(ctx context.Context, id int64, amount decimal.Decimal, holdingBase bool, lastPrice decimal.Decimal) error
}

// TelegramClient interface defines the operations needed for sending notifications
type TelegramClient interface {
	SendMessage(message string) error
}

// ReactToMarketCapStrategy implements a strategy that reacts to market capitalization changes
type ReactToMarketCapStrategy struct {
	*BaseStrategy
	config            *ReactToMarketCapConfig
	coinsMarketCap    map[string]decimal.Decimal
	pricesAtMarketCap map[string]decimal.Decimal
	loopCount         int
	mu                sync.RWMutex
	technicalIndicators *utils.TechnicalIndicators
	db                TradingDB
	telegram          TelegramClient
}

// NewReactToMarketCapStrategy creates a new ReactToMarketCapStrategy
func NewReactToMarketCapStrategy(ex exchange.Exchange, logger *utils.Logger, tradingDB TradingDB, telegram TelegramClient) *ReactToMarketCapStrategy {
	return &ReactToMarketCapStrategy{
		BaseStrategy:        NewBaseStrategy(ex, logger),
		config:              NewReactToMarketCapConfig(),
		coinsMarketCap:      make(map[string]decimal.Decimal),
		pricesAtMarketCap:   make(map[string]decimal.Decimal),
		loopCount:           0,
		technicalIndicators: utils.NewTechnicalIndicators(),
		db:                  tradingDB,
		telegram:            telegram,
	}
}

// Start initializes and starts the strategy
func (s *ReactToMarketCapStrategy) Start(ctx context.Context) error {
	s.Logger.Info("Starting React to Market Cap strategy...")

	// Check if trading coins are configured
	if len(s.config.TradingCoins) == 0 {
		return fmt.Errorf("TRADING_COINS is not set in environment variables")
	}

	// Start the base strategy
	if err := s.BaseStrategy.Start(ctx); err != nil {
		return err
	}

	// Load market caps
	if err := s.loadMarketCaps(ctx); err != nil {
		return err
	}

	// Initialize balances if needed
	if err := s.initializeBalances(ctx); err != nil {
		return err
	}

	// Check pending orders
	if err := s.checkPendingOrders(ctx); err != nil {
		return err
	}

	// Send notification
	if err := s.telegram.SendMessage("React to Market Cap strategy started"); err != nil {
		s.Logger.Errorf("Failed to send Telegram message: %v", err)
	}

	return nil
}

// Stop cleans up resources and stops the strategy
func (s *ReactToMarketCapStrategy) Stop(ctx context.Context) error {
	s.Logger.Info("Stopping React to Market Cap strategy...")

	// Send notification
	if err := s.telegram.SendMessage("React to Market Cap strategy stopped"); err != nil {
		s.Logger.Errorf("Failed to send Telegram message: %v", err)
	}

	return s.BaseStrategy.Stop(ctx)
}

// Loop executes one iteration of the strategy
func (s *ReactToMarketCapStrategy) Loop(ctx context.Context) error {
	// Increment loop counter
	s.loopCount++

	// Periodically check for pending orders and refresh market caps
	if s.loopCount%20 == 0 {
		if err := s.checkPendingOrders(ctx); err != nil {
			return err
		}
	}

	// Get all current balances using the ReactToMarketCap specific method
	reactBalances, err := s.db.ReactToMarketCapGetAllCurrentBalances(ctx)
	if err != nil {
		return fmt.Errorf("failed to get current balances: %w", err)
	}

	// Implement trading logic
	// For each trading pair, check if we should trade based on market cap EMAs
	for _, balance := range reactBalances {
		// Get historical market caps for EMA calculation
		longMarketCaps, err := s.calculateHistoricalMarketCaps(ctx, balance.LongTerm, s.config.EMALongInterval)
		if err != nil {
			s.Logger.Errorf("Error calculating long-term market caps for %d:%s: %v", balance.ID, balance.Pair, err)
			continue
		}

		shortMarketCaps, err := s.calculateHistoricalMarketCaps(ctx, balance.ShortTerm, s.config.EMAShortInterval)
		if err != nil {
			s.Logger.Errorf("Error calculating short-term market caps for %d:%s: %v", balance.ID, balance.Pair, err)
			continue
		}

		// Calculate EMAs
		longEMA := s.technicalIndicators.GetCurrentEMA(longMarketCaps, balance.LongTerm)
		shortEMA := s.technicalIndicators.GetCurrentEMA(shortMarketCaps, balance.ShortTerm)

		diff := shortEMA.Sub(longEMA).Div(longEMA)

		// Check if we should trade
		pair := balance.Pair

		// Buy signal
		if diff.GreaterThan(s.config.DeviationThreshold) && !balance.HoldingBase {
			s.Logger.Infof("Buy signal for %s: diff %s > threshold %s",
				pair, diff, s.config.DeviationThreshold)
			// Check if price has increased since last trade
			if balance.LastPrice != nil {
				currentPrice, err := s.Exchange.GetCurrentPrice(ctx, pair)
				if err != nil {
					s.Logger.Errorf("Error getting current price for %s: %v", pair, err)
					continue
				}

				if currentPrice.GreaterThan(*balance.LastPrice) {
					s.Logger.Infof("Skipping buy for %s: current price %s > last price %s",
						pair, currentPrice, balance.LastPrice)
					continue
				}
			}

			// Place order with exchange
			orderID, err := s.Exchange.CreateOrder(ctx, pair, "MARKET", "BUY", decimal.Zero, balance.Amount, decimal.Zero)
			if err != nil {
				return fmt.Errorf("failed to create buy order: %w", err)
			}

			// Update balance
			if err := s.db.ReactToMarketCapCurrentBalanceOrderPlaced(ctx, balance.ID, orderID); err != nil {
				return fmt.Errorf("failed to update balance: %w", err)
			}
		}

		// Sell signal
		if diff.LessThan(s.config.DeviationThreshold.Neg()) && balance.HoldingBase {
			s.Logger.Infof("Sell signal for %s: diff %s < threshold -%s",
				pair, diff, s.config.DeviationThreshold)

			// Place order with exchange
			orderID, err := s.Exchange.CreateOrder(ctx, pair, "MARKET", "SELL", balance.Amount, decimal.Zero, decimal.Zero)
			if err != nil {
				return fmt.Errorf("failed to create sell order: %w", err)
			}

			// Update balance
			if err := s.db.ReactToMarketCapCurrentBalanceOrderPlaced(ctx, balance.ID, orderID); err != nil {
				return fmt.Errorf("failed to update balance: %w", err)
			}
		}
	}

	return nil
}

// HandleOrderUpdate processes order execution reports
func (s *ReactToMarketCapStrategy) HandleOrderUpdate(report *exchange.ExecutionReport) error {
	s.Logger.Infof("Order update received: %s %s %s",
		report.Symbol, report.Side, report.Status)

	// If order is filled, update balance
	if report.Status == "FILLED" {
		// Get the trade details
		trade, err := s.Exchange.GetTrade(context.Background(), report.Symbol, report.ID)
		if err != nil {
			return fmt.Errorf("failed to get trade details: %w", err)
		}

		// get the right balance
		balances, err := s.db.ReactToMarketCapGetAllCurrentBalances(context.Background())
		if err != nil || len(balances) == 0 {
			return fmt.Errorf("failed to get balances: %w", err)
		}
		var balance *db.ReactToMarketCapBalance
		for _, b := range balances {
			if b.OrderID != nil && *b.OrderID == report.ClientOrderID {
				balance = &b
				break	
			}
		}
		if balance == nil {
			return fmt.Errorf("failed to find balance for order %s", report.ClientOrderID)
		}

		// Calculate final amount
		var finalAmount decimal.Decimal
		if strings.ToLower(report.Side) == "buy" {
			finalAmount = trade.Qty
		} else {
			finalAmount = trade.QuoteQty
		}

		// Deduct commissions
		for commissionAsset, commission := range trade.Commissions {
			if  commissionAsset == "BNB" {
			} else {
				// send top up message
				msg := fmt.Sprintf("Top up %s for commission: %s", commissionAsset, commission)
				s.Logger.Infof("%s", msg)
				if err := s.telegram.SendMessage(msg); err != nil {
					s.Logger.Errorf("Failed to send Telegram message: %v", err)
				}
				finalAmount = finalAmount.Sub(commission)
			}
			
		}

		// Update balance
		if err := s.db.ReactToMarketCapCurrentBalanceOrderCompleted(
			context.Background(),
			balance.ID,
			finalAmount,
			strings.ToLower(report.Side) == "buy",
			trade.Price,
		); err != nil {
			return fmt.Errorf("failed to update balance: %w", err)
		}

		// Send notification
		msg := fmt.Sprintf("Order filled: %s %s, Final Amount: %s",
			report.Symbol, report.Side, finalAmount)
		s.Logger.Infof("%s", msg)

		if err := s.telegram.SendMessage(msg); err != nil {
			s.Logger.Errorf("Failed to send Telegram message: %v", err)
		}
	}

	return nil
}

// GetStatus returns the current status of the strategy
func (s *ReactToMarketCapStrategy) GetStatus() map[string]interface{} {
	s.mu.RLock()
	defer s.mu.RUnlock()

	// Convert market caps to map[string]string for JSON serialization
	marketCaps := make(map[string]string)
	for coin, cap := range s.coinsMarketCap {
		marketCaps[coin] = cap.String()
	}

	return map[string]interface{}{
		"name":           "ReactToMarketCap",
		"running":        s.IsRunning,
		"market_caps":    marketCaps,
		"trading_coins":  s.config.TradingCoins,
		"initial_coin":   s.config.InitialCoin,
	}
}

// initializeBalances initializes balances if needed
func (s *ReactToMarketCapStrategy) initializeBalances(ctx context.Context) error {
	// Check if we already have balances
	balances, err := s.db.ReactToMarketCapGetAllCurrentBalances(ctx)
	if err != nil {
		return fmt.Errorf("failed to get current balances: %w", err)
	}

	if len(balances) > 0 {
		s.Logger.Infof("Found %d existing balances", len(balances))
		return nil
	}

	// No balances, initialize with initial coin
	initialCoin := s.config.InitialCoin
	balance, err := s.Exchange.GetBalance(ctx, initialCoin)
	if err != nil {
		return fmt.Errorf("failed to get balance for %s: %w", initialCoin, err)
	}

	initialAmount, ok := balance.Balances[initialCoin]
	if !ok || initialAmount.Free.IsZero() {
		return fmt.Errorf("no balance found for initial coin %s", initialCoin)
	}

	// calculate amount per pair
	cntCoins := 0
	for _, configs := range s.config.TradingCoins {
		cntCoins += len(configs)
	}
	amountPerPair := initialAmount.Free.Div(decimal.NewFromInt(int64(cntCoins))).Round(8)
	msg := fmt.Sprintf("Initializing with %s %s per pair, cnt_coins:%d",
		amountPerPair, initialCoin, cntCoins)
	s.Logger.Infof("%s", msg)
	if err := s.telegram.SendMessage(msg); err != nil {
		s.Logger.Errorf("Failed to send Telegram message: %v", err)
	}

	// Get configuration for trading pairs
	for coin, maConfigs := range s.config.TradingCoins {
		pair := coin + initialCoin
		for _, maConfig := range maConfigs {
			// Create balance record for each trading pair configuration
			_, err := s.db.ReactToMarketCapUpdateCurrentBalance(ctx, pair, maConfig.LongTerm, maConfig.ShortTerm, amountPerPair)
			if err != nil {
				return fmt.Errorf("failed to create balance record: %w", err)
			}
			s.Logger.Infof("Initialized balance for %s with %s %s (long_term=%d, short_term=%d)",
				pair, initialAmount.Free, initialCoin, maConfig.LongTerm, maConfig.ShortTerm)
		}
	}
	return nil
}

// checkPendingOrders checks for any pending orders
func (s *ReactToMarketCapStrategy) checkPendingOrders(ctx context.Context) error {
	s.Logger.Info("Checking pending orders...")
	balances, err := s.db.ReactToMarketCapGetAllCurrentBalances(ctx)
	if err != nil {
		return fmt.Errorf("failed to get current balances: %w", err)
	}
	// filter in-flight balances
	inFlightBalances := make([]db.ReactToMarketCapBalance, 0)
	for _, balance := range balances {
		if balance.InFlight {
			inFlightBalances = append(inFlightBalances, balance)
		}
	}
	if len(inFlightBalances) == 0 {
		s.Logger.Info("No pending orders found")
		return nil
	}

	for _, balance := range inFlightBalances {
		if balance.OrderID == nil {
			s.Logger.Infof("Balance %d marked as in_flight but has no order_id", balance.ID)
			if err := s.db.ReactToMarketCapCurrentBalanceOrderCompleted(ctx, balance.ID, balance.Amount, balance.HoldingBase, *balance.LastPrice); err != nil {
				return fmt.Errorf("failed to update balance: %w", err)
			}
			continue
		}

		// Check order status
		orderID := *balance.OrderID
		executionReport, err := s.Exchange.GetOrderStatus(ctx, balance.Pair, orderID)
		if err != nil {
			return fmt.Errorf("failed to get order status: %w", err)
		}

		// Process order update
		if err := s.HandleOrderUpdate(executionReport); err != nil {
			return fmt.Errorf("failed to handle order update: %w", err)
		}
	}
	return nil
}

// loadMarketCaps fetches market capitalization data from CoinGecko API
func (s *ReactToMarketCapStrategy) loadMarketCaps(ctx context.Context) error {
LOAD:
	s.Logger.Info("Updating market caps...")

	// Create HTTP client with timeout
	client := &http.Client{
		Timeout: 10 * time.Second,
	}

	// Prepare request
	req, err := http.NewRequestWithContext(ctx, "GET", s.config.CoingeckoAPIURL, nil)
	if err != nil {
		return fmt.Errorf("failed to create request: %w", err)
	}

	// Add query parameters
	q := req.URL.Query()
	q.Add("vs_currency", "usd")
	q.Add("order", "market_cap_desc")
	q.Add("per_page", "250")
	q.Add("page", "1")
	req.URL.RawQuery = q.Encode()

	// Execute request
	resp, err := client.Do(req)
	if err != nil {
		return fmt.Errorf("failed to execute request: %w", err)
	}
	defer resp.Body.Close()

	// Handle response
	s.Logger.Infof("Got market caps response, status: %d", resp.StatusCode)

	if resp.StatusCode == 200 {
		// Read response body
		body, err := io.ReadAll(resp.Body)
		if err != nil {
			return fmt.Errorf("failed to read response body: %w", err)
		}

		// Parse JSON
		var data []map[string]interface{}
		if err := json.Unmarshal(body, &data); err != nil {
			return fmt.Errorf("failed to parse JSON: %w", err)
		}

		// Update market caps
		tradingCoins := s.getAllTradingCoins()

		s.mu.Lock()
		defer s.mu.Unlock()

		// Clear existing data
		s.coinsMarketCap = make(map[string]decimal.Decimal)

		// Update with new data
		for _, coin := range data {
			symbol, ok := coin["symbol"].(string)
			if !ok {
				continue
			}

			symbol = strings.ToUpper(symbol)
			if !tradingCoins[symbol] {
				continue
			}

			marketCap, ok := coin["market_cap"].(float64)
			if !ok {
				continue
			}

			s.coinsMarketCap[symbol] = decimal.NewFromFloat(marketCap)
		}

		s.Logger.Infof("Updated market caps for %d coins", len(s.coinsMarketCap))

		// Update prices at the same time as market cap
		for coin := range tradingCoins {
			ticker, err := s.Exchange.GetTicker(ctx, coin+"USDT")
			if err != nil {
				s.Logger.Errorf("Error getting real-time price for %s: %v", coin, err)
				panic(err)
			}
			s.pricesAtMarketCap[coin] = ticker.LastPrice
		}

		s.Logger.Info("Market caps updated")
	} else if resp.StatusCode == 429 {
		s.Logger.Info("Rate limited by CoinGecko API, will retry later ...")
		time.Sleep(5 * time.Second)
		goto LOAD
	} else {
		return fmt.Errorf("failed to get market caps: %d", resp.StatusCode)
	}

	return nil
}

// getAllTradingCoins returns a set of all coins involved in trading
func (s *ReactToMarketCapStrategy) getAllTradingCoins() map[string]bool {
	coins := make(map[string]bool)

	// Add initial coin
	coins[s.config.InitialCoin] = true

	// Add all trading coins
	for coin := range s.config.TradingCoins {
		coins[coin] = true
	}

	return coins
}

// calculateHistoricalMarketCaps calculates historical market caps
func (s *ReactToMarketCapStrategy) calculateHistoricalMarketCaps(ctx context.Context, lookbackPeriods int, interval string) ([]decimal.Decimal, error) {
	coins := s.getAllTradingCoins()

	sumMarketCaps := make([]decimal.Decimal, lookbackPeriods)
	for i := range sumMarketCaps {
		sumMarketCaps[i] = decimal.Zero
	}

	// Get historical prices for each coin
	for coin := range coins {
		prices, err := s.Exchange.GetHistoricalPrices(ctx, coin+"USDT", lookbackPeriods, interval)
		if err != nil {
			s.Logger.Errorf("Error getting historical prices for %s: %v", coin, err)
			continue
		}

		if len(prices) != lookbackPeriods {
			// panic 
			panic(fmt.Sprintf("Invalid historical prices for %s, got %d expected %d",
				coin, len(prices), lookbackPeriods))
		}

		// Get hisorical market cap
		marketCap, ok := s.coinsMarketCap[coin]
		if !ok {
			continue
		}

		// Add to sum
		for i, price := range prices {
			priceAtMarketCap := s.pricesAtMarketCap[coin]
			ratio := price.Div(priceAtMarketCap)

			historicalMarketCap := marketCap.Mul(ratio)
			sumMarketCaps[i] = sumMarketCaps[i].Add(historicalMarketCap)
		}
	}

	return sumMarketCaps, nil
}
