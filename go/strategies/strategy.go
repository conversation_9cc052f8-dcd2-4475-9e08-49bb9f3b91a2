package strategies

import (
	"context"
	"fmt"

	"runtime/debug"

	"github.com/kenny/trading-bot/go/exchange"
	"github.com/kenny/trading-bot/go/utils"
	"github.com/shopspring/decimal"
)

// Strategy defines the interface for trading strategies
type Strategy interface {
	// Loop executes one iteration of the strategy
	Loop(ctx context.Context) error

	// Start initializes and starts the strategy
	Start(ctx context.Context) error

	// Stop cleans up resources and stops the strategy
	Stop(ctx context.Context) error

	// HandleOrderUpdate processes order execution reports
	HandleOrderUpdate(report *exchange.ExecutionReport) error

	// GetStatus returns the current status of the strategy
	GetStatus() map[string]interface{}

	UnrecoverableErrorf(ctx context.Context, format string, args ...interface{}) error
}

// BaseStrategy provides common functionality for all strategies
type BaseStrategy struct {
	Exchange exchange.Exchange
	Logger   *utils.Logger
	IsRunning bool
	telegram          TelegramClient
}

// NewBaseStrategy creates a new BaseStrategy
func NewBaseStrategy(ex exchange.Exchange, logger *utils.Logger, telegram TelegramClient) *BaseStrategy {
	return &BaseStrategy{
		Exchange: ex,
		Logger:   logger,
		IsRunning: false,
		telegram: telegram,
	}
}

// Start initializes the base strategy
func (b *BaseStrategy) Start(ctx context.Context) error {
	b.IsRunning = true
	b.Logger.Info("Base strategy started")
	return nil
}

// Stop cleans up resources for the base strategy
func (b *BaseStrategy) Stop(ctx context.Context) error {
	b.IsRunning = false
	b.Logger.Info("Base strategy stopped")
	return nil
}

// UnrecoverableError handles critical errors
func (b *BaseStrategy) UnrecoverableErrorf(ctx context.Context, format string, args ...interface{}) error {
	msg := fmt.Sprintf("Unrecoverable error: "+format, args...) + "\n" + string(debug.Stack())
	b.Logger.Error(msg)
	b.telegram.SendMessage(msg)
	b.IsRunning = false
	return nil
}

// DecimalPow calculates n^exponent for decimal values
func DecimalPow(n, exponent decimal.Decimal) decimal.Decimal {
	// Convert to float64 for calculation
	nFloat, _ := n.Float64()
	expFloat, _ := exponent.Float64()

	// Use math.Pow
	result := decimal.NewFromFloat(nFloat)
	if expFloat == 1.0 {
		return result
	}

	// For integer exponents, multiply
	if exponent.IsInteger() {
		exp := exponent.IntPart()
		if exp == 0 {
			return decimal.NewFromInt(1)
		}

		result = decimal.NewFromInt(1)
		for i := int64(0); i < exp; i++ {
			result = result.Mul(n)
		}
		return result
	}

	// For non-integer exponents, use logarithm
	return decimal.NewFromFloat(nFloat).Pow(exponent)
}
