# Build stage
FROM golang:1.23-alpine AS builder

WORKDIR /app

# Copy go mod files
COPY go.mod go.sum ./
RUN go mod download

# Copy source code
COPY go/ ./go/

# Build the application
WORKDIR /app/go
RUN CGO_ENABLED=0 GOOS=linux go build -a -installsuffix cgo -o trading-bot .

# Runtime stage
FROM alpine:latest

# Install ca-certificates for HTTPS requests
RUN apk --no-cache add ca-certificates tzdata

WORKDIR /root/

# Copy the binary from builder stage
COPY --from=builder /app/go/trading-bot .

# Copy database schema for initialization
COPY src/db/trading.sql /app/src/db/trading.sql

# Create logs directory
RUN mkdir -p /app/logs

CMD ["./trading-bot"]
