# Build stage
FROM golang:1.23-alpine AS builder

# Install git for go mod download (if needed)
RUN apk add --no-cache git

WORKDIR /app

# Copy go mod files first for better caching
COPY go.mod go.sum ./
RUN go mod download && go mod verify

# Copy source code
COPY go/ ./go/

# Build the application with security flags
WORKDIR /app/go
RUN CGO_ENABLED=0 GOOS=linux go build \
    -a -installsuffix cgo \
    -ldflags='-w -s -extldflags "-static"' \
    -o trading-bot .

# Runtime stage - use distroless for better security
FROM alpine:3.19

# Install ca-certificates for HTTPS requests and create non-root user
RUN apk --no-cache add ca-certificates tzdata && \
    addgroup -g 1001 -S appgroup && \
    adduser -u 1001 -S appuser -G appgroup && \
    rm -rf /var/cache/apk/*

# Create application directory and set ownership
WORKDIR /app
RUN mkdir -p /app/logs /app/data /app/src/db && \
    chown -R appuser:appgroup /app

# Copy the binary from builder stage and set ownership
COPY --from=builder --chown=appuser:appgroup /app/go/trading-bot .

# Make binary executable (just to be sure)
RUN chmod +x /app/trading-bot

# Switch to non-root user
USER appuser

# Set security-related environment variables
ENV CGO_ENABLED=0

# Expose any ports if needed (uncomment if your app serves HTTP)
# EXPOSE 8080

# Health check (optional - uncomment and modify as needed)
# HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
#   CMD pgrep trading-bot || exit 1
RUN mkdir -p /app/logs

CMD ["./trading-bot"]
