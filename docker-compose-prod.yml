version: '3.8'

services:
  db1:
    image: postgres:13
    environment:
      POSTGRES_DB: trading
      POSTGRES_USER: trading
      POSTGRES_PASSWORD: 123456a
    volumes:
      - ./src/db/trading.sql:/docker-entrypoint-initdb.d/trading.sql
      - pgdata-prod-1:/var/lib/postgresql/data
    ports:
      - "12431:5432"

  bot1:
    build: .
    environment:
      - DB_HOST=db1
      - DB_PORT=5432
      - DB_NAME=trading
      - DB_USER=trading
      - DB_PASSWORD=123456a
      - INITIAL_COIN=BTC
      - EXCHANGE_API_KEY=${EXCHANGE_API_KEY}
      - EXCHANGE_SECRET_KEY=${EXCHANGE_SECRET_KEY}
      - TELEGRAM_BOT_TOKEN=${TELEGRAM_TOKEN}
      - TELEGRAM_CHAT_ID=${TELEGRAM_CHAT_ID}
      - TRADING_MODE=live
      - TRADING_COINS={"ETH":[{"long_term":24,"short_term":2}],"SOL":[{"long_term":24,"short_term":2}],"XRP":[{"long_term":24,"short_term":2}],"DOGE":[{"long_term":24,"short_term":2}],"TRX":[{"long_term":24,"short_term":2}],"ADA":[{"long_term":24,"short_term":2}],"LINK":[{"long_term":24,"short_term":2}],"AVAX":[{"long_term":24,"short_term":2}],"XLM":[{"long_term":24,"short_term":2}],"TON":[{"long_term":24,"short_term":2}],"SUI":[{"long_term":24,"short_term":2}],"HBAR":[{"long_term":24,"short_term":2}],"BCH":[{"long_term":24,"short_term":2}],"DOT":[{"long_term":24,"short_term":2}],"LTC":[{"long_term":24,"short_term":2}]}
      - EMA_SHORT_INTERVAL=30m
      - EMA_LONG_INTERVAL=30m
      - DEVIATION_THRESHOLD=0
      - LOG_DIR=/app/logs
      - LOG_LEVEL=info

    volumes:
      - ./logs1:/app/logs
      - /etc/localtime:/etc/localtime:ro
    depends_on:
      - db1

volumes:
  pgdata-prod-1:
