version: '3.8'

services:
  db1:
    image: postgres:13
    environment:
      POSTGRES_DB: trading
      POSTGRES_USER: trading
      POSTGRES_PASSWORD: 123456a
    volumes:
      - ./src/db/trading.sql:/docker-entrypoint-initdb.d/trading.sql
      - pgdata-prod-1:/var/lib/postgresql/data
    ports:
      - "12431:5432"

  bot1:
    build: .
    environment:
      - DB_HOST=db1
      - DB_PORT=5432
      - DB_NAME=trading
      - DB_USER=trading
      - DB_PASSWORD=123456a
      - INITIAL_COIN=BTC
      - EXCHANGE_API_KEY=${EXCHANGE_API_KEY}
      - EXCHANGE_SECRET_KEY=${EXCHANGE_SECRET_KEY}
      - TELEGRAM_TOKEN=${TELEGRAM_TOKEN}
      - TELEGRAM_ALLOWED_USERS=${TELEGRAM_ALLOWED_USERS}
      - TRADING_MODE=live
      - STRATEGY_NAME=react_to_market_cap
      - TRADING_COINS={"ETH":[{"short_term":2,"long_term":24}],"SOL":[{"short_term":2,"long_term":24}],"XRP":[{"short_term":2,"long_term":24}],"DOGE":[{"short_term":2,"long_term":24}],"TRX":[{"short_term":2,"long_term":24}],"ADA":[{"short_term":2,"long_term":24}],"LINK":[{"short_term":2,"long_term":24}],"AVAX":[{"short_term":2,"long_term":24}],"XLM":[{"short_term":2,"long_term":24}],"TON":[{"short_term":2,"long_term":24}],"SUI":[{"short_term":2,"long_term":24}],"HBAR":[{"short_term":2,"long_term":24}],"BCH":[{"short_term":2,"long_term":24}],"DOT":[{"short_term":2,"long_term":24}],"LTC":[{"short_term":2,"long_term":24}]}
      - EMA_SHORT_INTERVAL=30m
      - EMA_LONG_INTERVAL=30m
      - DEVIATION_THRESHOLD=0

    volumes:
      - ./src:/app/src
      - ./setup.py:/app/setup.py
      - ./logs1:/app/logs
      - /etc/localtime:/etc/localtime:ro
    depends_on:
      - db1
    command: [sh, -c, "python src/main.py"]

volumes:
  pgdata-prod-1: 
