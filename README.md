# Trading Bot

A cryptocurrency trading bot implemented in Go, supporting various trading strategies.

## Features

- Multiple trading strategies
- Live trading and backtesting modes
- Integration with cryptocurrency exchanges
- Technical indicators for market analysis

## Strategies

### React to Market Cap Strategy

This strategy monitors the market capitalization of cryptocurrencies and makes trading decisions based on the relationship between short-term and long-term exponential moving averages (EMAs) of market caps.

#### Configuration

Set the following environment variables:

- `TRADING_MODE`: Set to "live" for live trading or "backtest" for backtesting
- `TRADING_COINS`: JSON string containing trading pairs and their EMA configurations
- `INITIAL_COIN`: Base currency for trading (default: "BTC")
- `EMA_LONG_INTERVAL`: Interval for long-term EMA (default: "1h")
- `EMA_SHORT_INTERVAL`: Interval for short-term EMA (default: "5m")
- `DEVIATION_THRESHOLD`: Threshold for trading signals (default: "0.001")
- `EXCHANGE_API_KEY`: API key for the exchange
- `EXCHANGE_SECRET_KEY`: Secret key for the exchange

For backtesting, also set:
- `BACKTEST_START_TIME`: Start time for backtesting in RFC3339 format
- `BACKTEST_END_TIME`: End time for backtesting in RFC3339 format

Example `TRADING_COINS` configuration:
```json
{
  "ETH": [
    {
      "long_term": 26,
      "short_term": 12
    }
  ],
  "BNB": [
    {
      "long_term": 24,
      "short_term": 8
    }
  ]
}
```

## Getting Started

1. Clone the repository
2. Install dependencies:
   ```bash
   go mod tidy
   ```
3. Create a `.env` file with your configuration (use `.env.example` as a template)
4. Run the bot:
   ```bash
   go run go/main.go
   ```

## Testing

Run the tests with:
```bash
go test ./go/...
```

## Development

The project is structured as follows:

- `go/`: Go source code
  - `exchange/`: Exchange interfaces and implementations
  - `strategies/`: Trading strategies
  - `utils/`: Utility functions
- `data/`: Data files for backtesting

## License

MIT
